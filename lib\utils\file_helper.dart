import 'dart:io';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';

class FileHelper {
  // نسخ ملف من المسار المطلق إلى مسار محلي
  static Future<bool> copyFile(String sourcePath, String destinationPath) async {
    try {
      // التحقق من وجود الملف المصدر
      final sourceFile = File(sourcePath);
      if (!await sourceFile.exists()) {
        debugPrint('الملف المصدر غير موجود: $sourcePath');
        return false;
      }
      
      // إنشاء المجلد الوجهة إذا لم يكن موجودًا
      final directory = Directory(destinationPath.substring(0, destinationPath.lastIndexOf('/')));
      if (!await directory.exists()) {
        await directory.create(recursive: true);
      }
      
      // نسخ الملف
      await sourceFile.copy(destinationPath);
      debugPrint('تم نسخ الملف بنجاح: $sourcePath إلى $destinationPath');
      return true;
    } catch (e) {
      debugPrint('خطأ في نسخ الملف: $e');
      return false;
    }
  }
  
  // الحصول على المسار المطلق للملف
  static Future<String> getAbsolutePath(String relativePath) async {
    try {
      final appDir = await getApplicationDocumentsDirectory();
      return '${appDir.path}/$relativePath';
    } catch (e) {
      debugPrint('خطأ في الحصول على المسار المطلق: $e');
      return relativePath;
    }
  }
  
  // التحقق من وجود ملف
  static Future<bool> fileExists(String path) async {
    try {
      final file = File(path);
      return await file.exists();
    } catch (e) {
      debugPrint('خطأ في التحقق من وجود الملف: $e');
      return false;
    }
  }
  
  // إنشاء ملف نصي
  static Future<bool> createTextFile(String path, String content) async {
    try {
      // إنشاء المجلد إذا لم يكن موجودًا
      final directory = Directory(path.substring(0, path.lastIndexOf('/')));
      if (!await directory.exists()) {
        await directory.create(recursive: true);
      }
      
      // إنشاء الملف
      final file = File(path);
      await file.writeAsString(content);
      debugPrint('تم إنشاء الملف النصي بنجاح: $path');
      return true;
    } catch (e) {
      debugPrint('خطأ في إنشاء الملف النصي: $e');
      return false;
    }
  }
  
  // نسخ ملفات السلسلة
  static Future<bool> copySeriesFiles(int seriesId) async {
    try {
      final appDir = await getApplicationDocumentsDirectory();
      final seriesDir = '${appDir.path}/series_$seriesId';
      
      // إنشاء المجلدات
      await Directory('$seriesDir/images').create(recursive: true);
      await Directory('$seriesDir/audio').create(recursive: true);
      
      // المسارات المطلقة للملفات
      final currentDir = Directory.current.path;
      
      if (seriesId == 1) {
        // نسخ الصور
        final sourceImagePath = '$currentDir/assets/images/series_1_image_1.jpg';
        final destImagePath = '$seriesDir/images/series_1_image_1.jpg';
        await copyFile(sourceImagePath, destImagePath);
        
        // نسخ الملفات الصوتية
        final sourceAudioPath = '$currentDir/assets/audio/series_1_audio_1.mp3';
        final destAudioPath = '$seriesDir/audio/series_1_audio_1.mp3';
        await copyFile(sourceAudioPath, destAudioPath);
        
        // حفظ الإجابات الصحيحة
        await createTextFile('$seriesDir/correct_answers.json', '{"1":[0]}');
      }
      
      return true;
    } catch (e) {
      debugPrint('خطأ في نسخ ملفات السلسلة: $e');
      return false;
    }
  }
}
