import 'package:flutter/material.dart';
import 'series_screen_new.dart';
import 'theory_lessons_screen_simple.dart';
import 'lessons_series_screen.dart';

class MainMenuScreen extends StatelessWidget {
  const MainMenuScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true, // تمديد الخلفية خلف شريط التطبيق
      appBar: AppBar(
        title: const Text(
          'القائمة الرئيسية',
          style: TextStyle(
            fontFamily: 'Cairo',
            fontWeight: FontWeight.bold,
            color: Colors.white,
            shadows: [
              Shadow(
                offset: Offset(1.0, 1.0),
                blurRadius: 3.0,
                color: Color.fromARGB(150, 0, 0, 0),
              ),
            ],
          ),
        ),
        backgroundColor: Colors.transparent, // جعل الشريط شفافًا
        elevation: 0, // إزالة الظل
        centerTitle: true,
        flexibleSpace: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Color.fromRGBO(0, 0, 0, 0.4),
                Color.fromRGBO(0, 0, 0, 0.1),
              ],
            ),
          ),
        ),
        automaticallyImplyLeading: false, // إلغاء زر العودة التلقائي
        actions: [
          IconButton(
            icon: const Icon(
              Icons.arrow_forward,
              color: Colors.white,
              shadows: [
                Shadow(
                  offset: Offset(1.0, 1.0),
                  blurRadius: 3.0,
                  color: Color.fromARGB(150, 0, 0, 0),
                ),
              ],
            ), // سهم للأمام (يمين) للعودة
            onPressed: () {
              Navigator.pop(context);
            },
          ),
        ],
      ),
      body: Stack(
        children: [
          // الخلفية مع التعتيم
          Container(
            decoration: BoxDecoration(
              image: DecorationImage(
                image: AssetImage(
                  MediaQuery.of(context).orientation == Orientation.portrait
                      ? 'assets/images/backgrounds/portrait_bg.jpg'
                      : 'assets/images/backgrounds/landscape_bg.jpg',
                ),
                fit: BoxFit.cover,
              ),
            ),
            child: Container(
              decoration: const BoxDecoration(
                color: Color.fromRGBO(0, 0, 0, 0.4),
              ),
            ),
          ),

          // الأزرار في الوسط
          Center(
            child: Padding(
              padding: const EdgeInsets.only(top: 30.0),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  _buildMenuButton(
                    context,
                    title: 'سلاسل الامتحان',
                    icon: Icons.library_books,
                    color: Colors.green,
                    onPressed: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const SeriesScreenNew(),
                        ),
                      );
                    },
                  ),
                  const SizedBox(height: 20),
                  _buildMenuButton(
                    context,
                    title: 'الدروس النظرية',
                    icon: Icons.menu_book,
                    color: Colors.orange,
                    onPressed: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) =>
                              const TheoryLessonsScreenSimple(),
                        ),
                      );
                    },
                  ),
                  const SizedBox(height: 20),
                  _buildMenuButton(
                    context,
                    title: 'سلاسل الدروس',
                    icon: Icons.school,
                    color: Colors.lightBlue,
                    onPressed: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const LessonsSeriesScreen(),
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),
          ),

          // عبارة الحقوق في الأسفل بدون خلفية داكنة، بخط أسمك، مرفوعة قليلاً
          const Positioned(
            bottom: 40, // رفعت المسافة إلى الأعلى
            left: 0,
            right: 0,
            child: Center(
              child: Text(
                'كل الحقوق محفوظة © 2025',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  fontFamily: 'Cairo',
                  shadows: [
                    Shadow(
                      offset: Offset(0.5, 0.5),
                      blurRadius: 2.0,
                      color: Colors.black54,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // دالة لإنشاء زر القائمة
  Widget _buildMenuButton(
    BuildContext context, {
    required String title,
    required IconData icon,
    required Color color,
    required VoidCallback onPressed,
  }) {
    return ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: color,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(horizontal: 40, vertical: 15),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(30),
        ),
        minimumSize: const Size(250, 60),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 30,
          ),
          const SizedBox(width: 15),
          Text(
            title,
            style: const TextStyle(
              fontFamily: 'Cairo',
              fontSize: 22,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }
}
