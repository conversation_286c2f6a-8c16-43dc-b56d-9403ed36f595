import '../models/series.dart';
import '../models/question.dart';
import '../models/user_answer.dart';

class DataService {
  // هذه بيانات تجريبية للتطبيق
  // في التطبيق الحقيقي، يمكن استبدالها بقراءة البيانات من ملف JSON أو قاعدة بيانات

  List<Series> getSeries() {
    List<Series> seriesList = [];

    for (int i = 1; i <= 10; i++) {
      seriesList.add(
        Series(
          id: i,
          title: 'السلسلة $i',
          description: 'تتضمن أسئلة متنوعة عن قواعد السير والسلامة المرورية',
          questions: _getQuestionsForSeries(i),
        ),
      );
    }

    return seriesList;
  }

  List<Question> _getQuestionsForSeries(int seriesId) {
    List<Question> questions = [];

    // سؤال عن الكحول والسياقة
    questions.add(
      Question(
        id: 1,
        imageUrl: 'assets/images/alcohol_question.jpg',
        audioUrl: 'assets/audio/question_${seriesId}_1.mp3',
        questionText: 'نسبة الراجلين من قتلى حوادث السير بالمغرب :',
        answers: [
          Answer(id: 1, text: '10%'),
          Answer(id: 2, text: '20%'),
          Answer(id: 3, text: 'فوق 25%'),
        ],
        correctAnswerIndices: [2], // الإجابة الثالثة هي الصحيحة
        explanation:
            'تشكل نسبة الراجلين من قتلى حوادث السير بالمغرب أكثر من 25% من مجموع الضحايا.',
      ),
    );

    // سؤال عن علامات المرور
    questions.add(
      Question(
        id: 2,
        imageUrl: 'assets/images/question_example.jpg',
        audioUrl: 'assets/audio/question_${seriesId}_2.mp3',
        questionText:
            'ما بين هاد العلامات : نقدر نحط الرايكلي معها : نعم .......... 1 لا .......... 2 - نقدر نتوقف بفرة : نعم .......... 3 لا .......... 4',
        answers: [
          Answer(id: 1, text: 'نعم'),
          Answer(id: 2, text: 'لا'),
          Answer(id: 3, text: 'نعم'),
          Answer(id: 4, text: 'لا'),
        ],
        correctAnswerIndices: [1, 3], // الإجابة الثانية والرابعة هي الصحيحة
        explanation:
            'لا يمكن وضع الدراجة الهوائية في هذه المنطقة ولا يمكن التوقف بفترة.',
      ),
    );

    // أسئلة إضافية
    for (int i = 3; i <= 40; i++) {
      questions.add(
        Question(
          id: i,
          imageUrl: 'assets/images/question_example.jpg',
          audioUrl: 'assets/audio/question_${seriesId}_$i.mp3',
          questionText: 'سؤال رقم $i في السلسلة $seriesId',
          answers: [
            Answer(id: 1, text: 'الإجابة الأولى'),
            Answer(id: 2, text: 'الإجابة الثانية'),
            Answer(id: 3, text: 'الإجابة الثالثة'),
            Answer(id: 4, text: 'الإجابة الرابعة'),
          ],
          correctAnswerIndices: [0], // الإجابة الأولى هي الصحيحة
          explanation: 'شرح للإجابة الصحيحة للسؤال رقم $i.',
        ),
      );
    }

    return questions;
  }

  List<UserAnswer> getUserAnswers(int seriesId) {
    // هذه دالة وهمية، في التطبيق الحقيقي ستقوم بجلب إجابات المستخدم من التخزين المحلي
    return [];
  }

  Future<void> saveUserAnswers(
      int seriesId, List<UserAnswer> userAnswers) async {
    // هذه دالة وهمية، في التطبيق الحقيقي ستقوم بحفظ إجابات المستخدم في التخزين المحلي
  }
}
