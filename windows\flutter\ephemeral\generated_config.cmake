# Generated code do not commit.
file(TO_CMAKE_PATH "C:\\src\\flutter" FLUTTER_ROOT)
file(TO_CMAKE_PATH "D:\\siya9a1" PROJECT_DIR)

set(FLUTTER_VERSION "3.0.1+17" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 3 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 0 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 1 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 17 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=C:\\src\\flutter"
  "PROJECT_DIR=D:\\siya9a1"
  "FLUTTER_ROOT=C:\\src\\flutter"
  "FLUTTER_EPHEMERAL_DIR=D:\\siya9a1\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=D:\\siya9a1"
  "FLUTTER_TARGET=D:\\siya9a1\\lib\\main.dart"
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=D:\\siya9a1\\.dart_tool\\package_config.json"
)
