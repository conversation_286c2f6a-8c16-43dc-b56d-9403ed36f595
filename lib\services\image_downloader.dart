import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:path_provider/path_provider.dart';
import 'package:flutter/material.dart';

Future<String> downloadImage(String url, String seriesId) async {
  final directory = await getApplicationDocumentsDirectory();
  final imageDir =
      Directory('${directory.path}/lessons_series/$seriesId/images');

  if (!await imageDir.exists()) {
    await imageDir.create(recursive: true);
  }

  final filename = Uri.parse(url).pathSegments.last;
  final filePath = '${imageDir.path}/$filename';
  final file = File(filePath);

  if (!await file.exists()) {
    final response = await http.get(Uri.parse(url));
    if (response.statusCode == 200) {
      await file.writeAsBytes(response.bodyBytes);
    } else {
      throw Exception("فشل تحميل الصورة: $url");
    }
  }

  return file.path;
}

Future<List<String>> downloadSeriesImages(
    List<String> imageUrls, String seriesId) async {
  List<String> localPaths = [];

  for (String url in imageUrls) {
    try {
      final path = await downloadImage(url, seriesId);
      localPaths.add(path);
    } catch (e) {
      debugPrint("خطأ في تحميل صورة: $e");
    }
  }

  return localPaths;
}
