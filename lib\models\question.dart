class Question {
  final int id;
  final String imageUrl;
  final String audioUrl;
  final String questionText;
  final List<Answer> answers;
  final List<int> correctAnswerIndices;
  final String explanation;

  Question({
    required this.id,
    required this.imageUrl,
    required this.audioUrl,
    required this.questionText,
    required this.answers,
    required this.correctAnswerIndices,
    required this.explanation,
  });

  factory Question.fromJson(Map<String, dynamic> json) {
    return Question(
      id: json['id'],
      imageUrl: json['imageUrl'],
      audioUrl: json['audioUrl'],
      questionText: json['questionText'],
      answers: (json['answers'] as List)
          .map((answer) => Answer.fromJson(answer))
          .toList(),
      correctAnswerIndices: List<int>.from(json['correctAnswerIndices']),
      explanation: json['explanation'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'imageUrl': imageUrl,
      'audioUrl': audioUrl,
      'questionText': questionText,
      'answers': answers.map((answer) => answer.toJson()).toList(),
      'correctAnswerIndices': correctAnswerIndices,
      'explanation': explanation,
    };
  }

  // 👇 هذه هي دالة النسخ
  Question copyWith({
    int? id,
    String? imageUrl,
    String? audioUrl,
    String? questionText,
    List<Answer>? answers,
    List<int>? correctAnswerIndices,
    String? explanation,
  }) {
    return Question(
      id: id ?? this.id,
      imageUrl: imageUrl ?? this.imageUrl,
      audioUrl: audioUrl ?? this.audioUrl,
      questionText: questionText ?? this.questionText,
      answers: answers ?? this.answers,
      correctAnswerIndices: correctAnswerIndices ?? this.correctAnswerIndices,
      explanation: explanation ?? this.explanation,
    );
  }
}

class Answer {
  final int id;
  final String text;

  Answer({
    required this.id,
    required this.text,
  });

  factory Answer.fromJson(Map<String, dynamic> json) {
    return Answer(
      id: json['id'],
      text: json['text'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'text': text,
    };
  }
}
