# Flutter Wrapper
-keep class io.flutter.app.** { *; }
-keep class io.flutter.plugin.**  { *; }
-keep class io.flutter.util.**  { *; }
-keep class io.flutter.view.**  { *; }
-keep class io.flutter.**  { *; }
-keep class io.flutter.plugins.**  { *; }

# AdMob
-keep class com.google.android.gms.ads.** { *; }

# Firebase
-keep class com.google.firebase.** { *; }

# حماية الكلاسات الخاصة بالتطبيق
-keep class com.mokhtar.siya9a.models.** { *; }
-keepclassmembers class com.mokhtar.siya9a.models.** { *; }

# تعتيم باقي الكود
-keepattributes SourceFile,LineNumberTable
-renamesourcefileattribute SourceFile
-repackageclasses 'com.mokhtar.siya9a.obfuscated'

# حماية الكود الأصلي
-keepattributes *Annotation*
-keepattributes Signature
-keepattributes Exceptions

# حماية JSON
-keepclassmembers class * {
    @com.google.gson.annotations.SerializedName <fields>;
}

# Play Libraries
-keep class com.google.android.play.core.** { *; }
-keep class com.google.android.play.app-update.** { *; }
-keep class com.google.android.play.review.** { *; }
-keep class com.google.android.play.feature-delivery.** { *; }
