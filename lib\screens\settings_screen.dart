import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';
import '../providers/settings_provider.dart';
import '../providers/quiz_provider.dart';

class SettingsScreen extends StatelessWidget {
  // رابط التطبيق على متجر Play Store (استبدله برابط تطبيقك الفعلي)
  static const String _playStoreUrl =
      'https://play.google.com/store/apps/details?id=com.yourcompany.siya9a';

  const SettingsScreen({Key? key}) : super(key: key);

  // دالة لفتح صفحة التطبيق على متجر Play Store
  Future<void> _openPlayStore() async {
    final Uri url = Uri.parse(_playStoreUrl);
    if (await canLaunchUrl(url)) {
      await launchUrl(url, mode: LaunchMode.externalApplication);
    } else {
      debugPrint('لا يمكن فتح الرابط: $_playStoreUrl');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: const Color(0xFF0D47A1), // لون أزرق غامق
        title: const Text(
          'الإعدادات',
          style: TextStyle(
            fontFamily: 'Cairo',
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
      ),
      body: Consumer<SettingsProvider>(
        builder: (context, settingsProvider, child) {
          if (settingsProvider.isLoading) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          final currentTimerDuration = settingsProvider.settings.timerDuration;

          return Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'إعدادات المؤقت',
                  style: TextStyle(
                    fontFamily: 'Cairo',
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                const Text(
                  'اختر المدة الزمنية لكل سؤال:',
                  style: TextStyle(
                    fontFamily: 'Cairo',
                    fontSize: 16,
                  ),
                ),
                const SizedBox(height: 16),

                // خيارات المؤقت
                _buildTimerOption(
                  context,
                  title: '10 ثواني',
                  value: 10,
                  isSelected: currentTimerDuration == 10,
                  onTap: () => _updateTimerDuration(context, 10),
                ),

                _buildTimerOption(
                  context,
                  title: '20 ثانية',
                  value: 20,
                  isSelected: currentTimerDuration == 20,
                  onTap: () => _updateTimerDuration(context, 20),
                ),

                _buildTimerOption(
                  context,
                  title: '30 ثانية',
                  value: 30,
                  isSelected: currentTimerDuration == 30,
                  onTap: () => _updateTimerDuration(context, 30),
                ),

                const SizedBox(height: 32),
                const Divider(),
                const SizedBox(height: 16),

                // قسم الروابط
                const Text(
                  'روابط مفيدة',
                  style: TextStyle(
                    fontFamily: 'Cairo',
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),

                // زر تقييم التطبيق
                InkWell(
                  onTap: _openPlayStore,
                  child: Container(
                    padding: const EdgeInsets.all(16.0),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      border: Border.all(color: Colors.grey),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Row(
                      children: [
                        Icon(Icons.star, color: Colors.amber),
                        SizedBox(width: 16),
                        Text(
                          'تقييم التطبيق',
                          style: TextStyle(
                            fontFamily: 'Cairo',
                            fontSize: 16,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  // دالة لتحديث مدة المؤقت في جميع المزودين
  void _updateTimerDuration(BuildContext context, int seconds) {
    // تحديث في SettingsProvider
    final settingsProvider =
        Provider.of<SettingsProvider>(context, listen: false);
    settingsProvider.updateTimerDuration(seconds);

    // تحديث في QuizProvider
    final quizProvider = Provider.of<QuizProvider>(context, listen: false);
    quizProvider.setTimerDuration(seconds);

    // عرض رسالة تأكيد
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'تم تحديث مدة المؤقت إلى $seconds ثانية',
          style: const TextStyle(fontFamily: 'Cairo'),
        ),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  Widget _buildTimerOption(
    BuildContext context, {
    required String title,
    required int value,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: InkWell(
        onTap: onTap,
        child: Container(
          padding: const EdgeInsets.all(16.0),
          decoration: BoxDecoration(
            color: isSelected ? Colors.blue.shade100 : Colors.white,
            border: Border.all(
              color: isSelected ? Colors.blue : Colors.grey,
              width: isSelected ? 2 : 1,
            ),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            children: [
              Icon(
                isSelected
                    ? Icons.radio_button_checked
                    : Icons.radio_button_unchecked,
                color: isSelected ? Colors.blue : Colors.grey,
              ),
              const SizedBox(width: 16),
              Text(
                title,
                style: TextStyle(
                  fontFamily: 'Cairo',
                  fontSize: 16,
                  fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
