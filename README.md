# سياقة (Siya9a)

تطبيق لتعليم قانون السير بالمغرب، يحتوي على سلاسل من الأسئلة التفاعلية والدروس النظرية.

## متطلبات النشر على Google Play

### 1. إعداد مفاتيح التوقيع

قبل نشر التطبيق على Google Play، يجب إنشاء مفتاح توقيع:

```bash
keytool -genkey -v -keystore ~/upload-keystore.jks -keyalg RSA -keysize 2048 -validity 10000 -alias upload
```

ثم قم بإنشاء ملف `android/key.properties` بالمحتوى التالي:

```
storePassword=<كلمة مرور المخزن>
keyPassword=<كلمة مرور المفتاح>
keyAlias=upload
storeFile=<مسار كامل إلى ملف upload-keystore.jks>
```

### 2. إعداد AdMob

قبل النشر، استبدل معرف تطبيق AdMob الاختباري بمعرف حقيقي في ملف `android/app/src/main/AndroidManifest.xml`:

```xml
<meta-data
    android:name="com.google.android.gms.ads.APPLICATION_ID"
    android:value="معرف-التطبيق-الحقيقي"/>
```

### 3. إنشاء حزمة التطبيق للنشر

```bash
flutter build appbundle --release
```

ستجد ملف `.aab` في المسار `build/app/outputs/bundle/release/app-release.aab`

### 4. متطلبات Google Play الأخرى

- إنشاء سياسة خصوصية
- إعداد صور العرض (لقطات شاشة)
- كتابة وصف التطبيق
- إكمال استبيان تصنيف المحتوى

## ملاحظات هامة

- احتفظ بملف مفتاح التوقيع في مكان آمن، فقدانه يعني عدم القدرة على تحديث التطبيق
- لا تقم أبدًا بتضمين ملفات التوقيع في نظام التحكم بالإصدار
