import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';

class ChannelsScreen extends StatelessWidget {
  const ChannelsScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: const Color(0xFF0D47A1), // لون أزرق غامق
        title: const Text(
          'قنواتنا',
          style: TextStyle(
            fontFamily: 'Cairo',
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'تابعنا على وسائل التواصل الاجتماعي',
              style: TextStyle(
                fontFamily: 'Cairo',
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 24),
            _buildSocialMediaButton(
              context,
              icon: Icons.facebook,
              title: 'فيسبوك',
              color: Colors.blue.shade800,
              url: 'https://www.facebook.com/profile.php?id=61575458721384',
            ),
            const SizedBox(height: 16),
            _buildSocialMediaButton(
              context,
              icon: Icons.camera_alt,
              title: 'إنستغرام',
              color: Colors.pink.shade400,
              url:
                  'https://www.instagram.com/prof_mokhtar1?igsh=N3E1MWliN2huOXox',
            ),
            const SizedBox(height: 16),
            _buildSocialMediaButton(
              context,
              icon: Icons.play_arrow,
              title: 'يوتيوب',
              color: Colors.red.shade600,
              url: 'https://www.youtube.com/channel/UCv26RRLuxijcq_ZXEaN5Mrw',
            ),
            const SizedBox(height: 16),
            _buildSocialMediaButton(
              context,
              icon: Icons.send,
              title: 'تيليغرام',
              color: Colors.blue.shade500,
              url: 'https://t.me/ProFMokhtaR1',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSocialMediaButton(
    BuildContext context, {
    required IconData icon,
    required String title,
    required Color color,
    required String url,
  }) {
    return InkWell(
      onTap: () => _launchUrl(url),
      child: Container(
        padding: const EdgeInsets.all(16.0),
        decoration: BoxDecoration(
          color: color.withAlpha(25),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: color.withAlpha(75),
          ),
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: color,
                shape: BoxShape.circle,
              ),
              child: Icon(
                icon,
                color: Colors.white,
                size: 28,
              ),
            ),
            const SizedBox(width: 16),
            Text(
              title,
              style: TextStyle(
                fontFamily: 'Cairo',
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            const Spacer(),
            Icon(
              Icons.arrow_forward_ios,
              color: color,
              size: 20,
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _launchUrl(String urlString) async {
    final Uri url = Uri.parse(urlString);
    if (!await launchUrl(url, mode: LaunchMode.externalApplication)) {
      throw Exception('Could not launch \$url');
    }
  }
}
