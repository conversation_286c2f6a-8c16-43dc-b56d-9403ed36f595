import 'package:flutter/material.dart';

class ProhibitionSignsScreen extends StatefulWidget {
  const ProhibitionSignsScreen({Key? key}) : super(key: key);

  @override
  State<ProhibitionSignsScreen> createState() => _ProhibitionSignsScreenState();
}

class _ProhibitionSignsScreenState extends State<ProhibitionSignsScreen> {
  // قائمة بمعلومات علامات المنع
  final List<Map<String, dynamic>> _prohibitionSigns = [
    {
      'image': 'assets/images/road_signs/prohibition/sign1.png',
      'description': 'ممنوع المرور',
    },
    {
      'image': 'assets/images/road_signs/prohibition/sign2.png',
      'description': 'ممنوع الوقوف',
    },
    {
      'image': 'assets/images/road_signs/prohibition/sign3.png',
      'description': 'ممنوع التجاوز',
    },
    {
      'image': 'assets/images/road_signs/prohibition/sign4.png',
      'description': 'ممنوع استعمال المنبه',
    },
    {
      'image': 'assets/images/road_signs/prohibition/sign5.png',
      'description': 'ممنوع الدوران للخلف',
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'علامات المنع',
          style: TextStyle(
            fontFamily: 'Cairo',
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: const Color(0xFF2196F3),
        centerTitle: true,
        // إزالة leading وإضافة زر العودة في actions على اليمين
        automaticallyImplyLeading: false, // إلغاء زر العودة التلقائي
        actions: [
          IconButton(
            icon: const Icon(Icons.arrow_forward), // سهم للأمام (يمين) للعودة
            onPressed: () {
              Navigator.pop(context);
            },
          ),
        ],
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.blue.shade400,
              Colors.grey.shade800,
            ],
          ),
        ),
        child: OrientationBuilder(
          builder: (context, orientation) {
            // التحقق من اتجاه الشاشة لتحديد عدد الأعمدة
            final isLandscape = orientation == Orientation.landscape;

            return GridView.builder(
              padding: const EdgeInsets.all(16),
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: isLandscape
                    ? 2
                    : 1, // عمودان في الوضع الأفقي، عمود واحد في الوضع الرأسي
                childAspectRatio:
                    1, // نسبة العرض إلى الارتفاع 1:1 للحفاظ على الشكل المربع
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
              ),
              itemCount: _prohibitionSigns.length,
              itemBuilder: (context, index) {
                return _buildSignCard(
                  imagePath: _prohibitionSigns[index]['image'],
                  description: _prohibitionSigns[index]['description'],
                );
              },
            );
          },
        ),
      ),
    );
  }

  // دالة لبناء بطاقة علامة المنع
  Widget _buildSignCard({
    required String imagePath,
    required String description,
  }) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // حساب عرض البطاقة بناءً على عرض الشاشة
        // استخدام نسبة من عرض الشاشة مع هوامش
        final screenWidth = MediaQuery.of(context).size.width;
        final cardSize = screenWidth > 600
            ? screenWidth * 0.4 // للشاشات الكبيرة
            : screenWidth * 0.8; // للشاشات الصغيرة

        // التأكد من أن البطاقة مربعة
        final cardWidth = cardSize;
        final cardHeight = cardSize;

        return Center(
          child: Card(
            margin: const EdgeInsets.only(bottom: 16),
            elevation: 4,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            child: AspectRatio(
              aspectRatio:
                  1, // نسبة العرض إلى الارتفاع 1:1 للحفاظ على الشكل المربع
              child: ClipRRect(
                borderRadius: BorderRadius.circular(12),
                child: Image.asset(
                  imagePath,
                  width: cardWidth,
                  height: cardHeight,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    // في حالة حدوث خطأ في تحميل الصورة
                    return Container(
                      width: cardWidth,
                      height: cardHeight,
                      color: Colors.grey.shade200,
                      child: const Center(
                        child: Icon(
                          Icons.image_not_supported,
                          size: 50,
                          color: Colors.grey,
                        ),
                      ),
                    );
                  },
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
