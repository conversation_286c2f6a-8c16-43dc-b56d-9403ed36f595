import 'package:flutter/material.dart';

class MandatorySignsScreen extends StatefulWidget {
  const MandatorySignsScreen({Key? key}) : super(key: key);

  @override
  State<MandatorySignsScreen> createState() => _MandatorySignsScreenState();
}

class _MandatorySignsScreenState extends State<MandatorySignsScreen> {
  // قائمة بمعلومات علامات الإجبار
  final List<Map<String, dynamic>> _mandatorySigns = [
    {
      'image': 'assets/images/road_signs/mandatory/sign1.png',
      'description': 'إلزامية السير للأمام',
    },
    {
      'image': 'assets/images/road_signs/mandatory/sign2.png',
      'description': 'إلزامية السير لليمين',
    },
    {
      'image': 'assets/images/road_signs/mandatory/sign3.png',
      'description': 'إلزامية السير لليسار',
    },
    {
      'image': 'assets/images/road_signs/mandatory/sign4.png',
      'description': 'دوار إلزامي',
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'علامات الإجبار',
          style: TextStyle(
            fontFamily: 'Cairo',
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: const Color(0xFF2196F3),
        centerTitle: true,
        // إزالة leading وإضافة زر العودة في actions على اليمين
        automaticallyImplyLeading: false, // إلغاء زر العودة التلقائي
        actions: [
          IconButton(
            icon: const Icon(Icons.arrow_forward), // سهم للأمام (يمين) للعودة
            onPressed: () {
              Navigator.pop(context);
            },
          ),
        ],
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.blue.shade400,
              Colors.grey.shade800,
            ],
          ),
        ),
        child: OrientationBuilder(
          builder: (context, orientation) {
            // التحقق من اتجاه الشاشة لتحديد عدد الأعمدة
            final isLandscape = orientation == Orientation.landscape;

            return GridView.builder(
              padding: const EdgeInsets.all(16),
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: isLandscape
                    ? 2
                    : 1, // عمودان في الوضع الأفقي، عمود واحد في الوضع الرأسي
                childAspectRatio:
                    1, // نسبة العرض إلى الارتفاع 1:1 للحفاظ على الشكل المربع
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
              ),
              itemCount: _mandatorySigns.length,
              itemBuilder: (context, index) {
                return _buildSignCard(
                  imagePath: _mandatorySigns[index]['image'],
                  description: _mandatorySigns[index]['description'],
                );
              },
            );
          },
        ),
      ),
    );
  }

  // دالة لبناء بطاقة علامة الإجبار
  Widget _buildSignCard({
    required String imagePath,
    required String description,
  }) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // حساب عرض البطاقة بناءً على عرض الشاشة
        // استخدام نسبة من عرض الشاشة مع هوامش
        final screenWidth = MediaQuery.of(context).size.width;
        final cardSize = screenWidth > 600
            ? screenWidth * 0.4 // للشاشات الكبيرة
            : screenWidth * 0.8; // للشاشات الصغيرة

        // التأكد من أن البطاقة مربعة
        final cardWidth = cardSize;
        final cardHeight = cardSize;

        return Center(
          child: Card(
            margin: const EdgeInsets.only(bottom: 16),
            elevation: 4,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            child: AspectRatio(
              aspectRatio:
                  1, // نسبة العرض إلى الارتفاع 1:1 للحفاظ على الشكل المربع
              child: ClipRRect(
                borderRadius: BorderRadius.circular(12),
                child: Image.asset(
                  imagePath,
                  width: cardWidth,
                  height: cardHeight,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    // في حالة حدوث خطأ في تحميل الصورة
                    return Container(
                      width: cardWidth,
                      height: cardHeight,
                      color: Colors.grey.shade200,
                      child: const Center(
                        child: Icon(
                          Icons.image_not_supported,
                          size: 50,
                          color: Colors.grey,
                        ),
                      ),
                    );
                  },
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
