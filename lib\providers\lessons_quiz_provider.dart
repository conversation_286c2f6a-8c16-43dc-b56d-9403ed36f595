import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/series.dart';
import '../models/question.dart';
import '../models/user_answer.dart';
import '../services/audio_service.dart';
import '../services/settings_service.dart';
import '../services/ad_service.dart';

enum LessonsQuizStatus { initial, inProgress, completed }

class LessonsQuizProvider with ChangeNotifier {
  final AudioService _audioService = AudioService();
  final SettingsService _settingsService = SettingsService();
  final AdService _adService = AdService();

  final List<Series> _lessonsSeries = [];
  Series? _currentSeries;
  int _currentQuestionIndex = 0;
  List<UserAnswer> _userAnswers = [];
  List<int> _selectedAnswerIndices = [];
  LessonsQuizStatus _status = LessonsQuizStatus.initial;
  Timer? _timer;
  int _remainingTime = 30;
  int _timerDuration = 30;

  LessonsQuizProvider() {
    _initializeAdService();
    _loadSettings();
  }

  // تهيئة خدمة الإعلانات
  Future<void> _initializeAdService() async {
    // تخطي تهيئة الإعلانات إذا كان التطبيق يعمل على الويب
    if (kIsWeb) {
      debugPrint(
          'تم تخطي تهيئة الإعلانات في LessonsQuizProvider لأن التطبيق يعمل على الويب');
      return;
    }

    try {
      await _adService.initialize();
    } catch (e) {
      debugPrint('فشل في تهيئة خدمة الإعلانات في LessonsQuizProvider: $e');
    }
  }

  // تحميل إعدادات المؤقت
  Future<void> _loadSettings() async {
    try {
      final settings = await _settingsService.getSettings();
      _timerDuration = settings.timerDuration;
      notifyListeners();
    } catch (e) {
      debugPrint('خطأ في تحميل الإعدادات: $e');
    }
  }

  List<Series> get lessonsSeries => _lessonsSeries;
  Series? get currentSeries => _currentSeries;
  Question? get currentQuestion => _currentSeries != null &&
          _currentQuestionIndex < _currentSeries!.questions.length
      ? _currentSeries!.questions[_currentQuestionIndex]
      : null;
  int get currentQuestionIndex => _currentQuestionIndex;

  // الحصول على إجابات المستخدم مرتبة حسب معرف السؤال
  List<UserAnswer> get userAnswers {
    // نسخة من الإجابات مرتبة حسب معرف السؤال
    final sortedAnswers = List<UserAnswer>.from(_userAnswers);
    sortedAnswers.sort((a, b) => a.questionId.compareTo(b.questionId));
    return sortedAnswers;
  }

  List<int> get selectedAnswerIndices => _selectedAnswerIndices;
  LessonsQuizStatus get status => _status;
  int get remainingTime => _remainingTime;
  int get timerDuration => _timerDuration;

  int get totalQuestions => _currentSeries?.questions.length ?? 0;
  int get correctAnswersCount =>
      _userAnswers.where((answer) => answer.isCorrect).length;

  // دالة لتحميل بيانات السلسلة مسبقًا بدون بدء الاختبار
  Future<void> preloadSeries(Series series) async {
    // تعيين السلسلة الحالية فقط
    _currentSeries = series;
    _currentQuestionIndex = 0;

    // تحميل الصوت مسبقًا
    if (currentQuestion != null) {
      // تهيئة الصوت مسبقًا (بدون تشغيله)
      try {
        debugPrint('تحميل الصوت مسبقًا: ${currentQuestion!.audioUrl}');
        await _audioService.preloadAudio(currentQuestion!.audioUrl);
      } catch (e) {
        debugPrint('خطأ في تحميل الصوت مسبقًا: $e');
      }
    }

    // محاولة استعادة إجابات المستخدم من التخزين المحلي
    await _loadUserAnswers(series.id);
  }

  // دالة لاستعادة إجابات المستخدم من التخزين المحلي
  Future<void> _loadUserAnswers(int seriesId) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // استعادة إجابات المستخدم
      final userAnswersJson = prefs.getString('lessons_user_answers_$seriesId');

      if (userAnswersJson != null && userAnswersJson.isNotEmpty) {
        final List<dynamic> decodedAnswers = jsonDecode(userAnswersJson);
        _userAnswers =
            decodedAnswers.map((json) => UserAnswer.fromJson(json)).toList();

        // استعادة حالة الاختبار
        final statusIndex = prefs.getInt('lessons_quiz_status_$seriesId');
        if (statusIndex != null) {
          _status = LessonsQuizStatus.values[statusIndex];
        }

        // استعادة نتيجة آخر اختبار
        final lastScore = prefs.getInt('lessons_last_score_$seriesId');
        if (lastScore != null && _currentSeries != null) {
          _currentSeries!.lastScore = lastScore;
          debugPrint('تم استعادة نتيجة آخر اختبار: $lastScore');
        }

        debugPrint('تم استعادة ${_userAnswers.length} إجابة للسلسلة $seriesId');
        debugPrint('حالة الاختبار: $_status');
      } else {
        debugPrint('لا توجد إجابات محفوظة للسلسلة $seriesId');
      }
    } catch (e) {
      debugPrint('خطأ في استعادة إجابات المستخدم: $e');
    }

    notifyListeners();
  }

  Future<void> startSeries(Series series) async {
    // تعيين حالة البدء قبل تحميل الصوت
    _currentSeries = series;
    _currentQuestionIndex = 0;
    _userAnswers = [];
    _selectedAnswerIndices = [];
    _status = LessonsQuizStatus.inProgress;

    // تعيين المؤقت إلى قيمته الأولية
    _remainingTime = _timerDuration;

    // تعيين حالة المؤقت إلى متوقف
    _isTimerPaused = true;

    // تحميل الإعلان مسبقًا في الخلفية
    _preloadAd();

    // إخطار المستمعين بالتغييرات الأولية
    notifyListeners();

    // تشغيل الصوت وبدء المؤقت بعد انتهاء الصوت
    await _playQuestionAudio();
  }

  // تحميل الإعلان مسبقًا
  void _preloadAd() {
    // تخطي تحميل الإعلان إذا كان التطبيق يعمل على الويب
    if (kIsWeb) {
      debugPrint('تم تخطي تحميل الإعلان مسبقًا لأن التطبيق يعمل على الويب');
      return;
    }

    debugPrint('جاري تحميل الإعلان مسبقًا...');
    _adService.loadInterstitialAd();
  }

  void selectAnswer(int answerIndex) {
    if (_status != LessonsQuizStatus.inProgress) return;

    // استخدام المؤشر كما هو (يبدأ من 0 في الكود، ولكن يمثل 1 في واجهة المستخدم)
    if (_selectedAnswerIndices.contains(answerIndex)) {
      _selectedAnswerIndices.remove(answerIndex);
    } else {
      _selectedAnswerIndices.add(answerIndex);
    }
    notifyListeners();
  }

  void clearSelectedAnswers() {
    _selectedAnswerIndices = [];
    notifyListeners();
  }

  Future<void> submitAnswer() async {
    if (_status != LessonsQuizStatus.inProgress || currentQuestion == null) {
      return;
    }

    final question = currentQuestion!;
    final isCorrect = _areAnswersCorrect(
        _selectedAnswerIndices, question.correctAnswerIndices);

    _userAnswers.add(UserAnswer(
      questionId: question.id,
      selectedAnswerIndices: List.from(_selectedAnswerIndices),
      isCorrect: isCorrect,
    ));

    _cancelTimer();

    if (_currentQuestionIndex < _currentSeries!.questions.length - 1) {
      await _goToNextQuestion();
    } else {
      _completeQuiz();
    }
  }

  bool _areAnswersCorrect(List<int> selectedIndices, List<int> correctIndices) {
    if (selectedIndices.length != correctIndices.length) return false;

    // تحويل المؤشرات المحددة إلى مؤشرات تبدأ من 1
    List<int> selectedIndicesFrom1 =
        selectedIndices.map((index) => index + 1).toList();

    // مقارنة المؤشرات المحددة مع المؤشرات الصحيحة
    for (final index in correctIndices) {
      if (!selectedIndicesFrom1.contains(index)) return false;
    }

    for (final index in selectedIndicesFrom1) {
      if (!correctIndices.contains(index)) return false;
    }

    return true;
  }

  Future<void> _goToNextQuestion() async {
    _currentQuestionIndex++;
    _selectedAnswerIndices = [];

    // إيقاف المؤقت مؤقتًا
    _cancelTimer();

    // تعيين المؤقت إلى قيمته الأولية
    _remainingTime = _timerDuration;

    // تعيين حالة المؤقت إلى متوقف
    _isTimerPaused = true;

    // إخطار المستمعين بالتغييرات
    notifyListeners();

    // تشغيل الصوت وبدء المؤقت بعد انتهاء الصوت
    await _playQuestionAudio();
  }

  void _completeQuiz() {
    _status = LessonsQuizStatus.completed;
    _audioService.stopAudio();

    // حساب عدد الإجابات الصحيحة
    int correctCount = 0;
    for (var answer in _userAnswers) {
      if (answer.isCorrect) {
        correctCount++;
      }
    }

    // تحديث نتيجة آخر اختبار في السلسلة الحالية
    if (_currentSeries != null) {
      _currentSeries!.lastScore = correctCount;
      debugPrint(
          'تم تحديث نتيجة آخر اختبار: $correctCount/${_currentSeries!.totalQuestions}');
    }

    // حفظ إجابات المستخدم في التخزين المحلي
    _saveUserAnswers();

    // عرض الإعلان إذا كان جاهزًا
    _showAdIfReady();

    notifyListeners();
  }

  // عرض الإعلان إذا كان جاهزًا
  Future<void> _showAdIfReady() async {
    // تخطي عرض الإعلان إذا كان التطبيق يعمل على الويب
    if (kIsWeb) {
      debugPrint('تم تخطي عرض الإعلان لأن التطبيق يعمل على الويب');
      return;
    }

    if (_adService.isInterstitialAdReady) {
      debugPrint('عرض الإعلان البيني بعد اكتمال السلسلة');
      await _adService.showInterstitialAd();
    } else {
      debugPrint('الإعلان غير جاهز للعرض بعد اكتمال السلسلة');
    }
  }

  // حفظ إجابات المستخدم في التخزين المحلي
  Future<void> _saveUserAnswers() async {
    if (_currentSeries == null) return;

    try {
      final prefs = await SharedPreferences.getInstance();

      // تحويل إجابات المستخدم إلى قائمة من الخرائط
      final userAnswersJson =
          _userAnswers.map((answer) => answer.toJson()).toList();

      // حفظ إجابات المستخدم كسلسلة JSON
      await prefs.setString(
        'lessons_user_answers_${_currentSeries!.id}',
        jsonEncode(userAnswersJson),
      );

      // حفظ حالة الاختبار
      await prefs.setInt(
          'lessons_quiz_status_${_currentSeries!.id}', _status.index);

      // حفظ نتيجة آخر اختبار
      await prefs.setInt('lessons_last_score_${_currentSeries!.id}',
          _currentSeries!.lastScore);

      debugPrint('تم حفظ إجابات المستخدم للسلسلة ${_currentSeries!.id}');
      debugPrint('تم حفظ نتيجة آخر اختبار: ${_currentSeries!.lastScore}');
    } catch (e) {
      debugPrint('خطأ في حفظ إجابات المستخدم: $e');
    }
  }

  // متغير لتتبع حالة المؤقت
  bool _isTimerPaused = false;
  bool get isTimerPaused => _isTimerPaused;

  void _startTimer() {
    _remainingTime = _timerDuration;
    _cancelTimer();
    _isTimerPaused = false;

    _timer = Timer.periodic(const Duration(seconds: 1), (timer) async {
      if (_remainingTime > 0) {
        if (!_isTimerPaused) {
          _remainingTime--;
          notifyListeners();
        }
      } else {
        await submitAnswer();

        // إضافة تأخير صغير للتأكد من أن حالة الاختبار تم تحديثها
        Future.delayed(const Duration(milliseconds: 100), () {
          if (_status == LessonsQuizStatus.completed) {
            // إخطار المستمعين بأن الاختبار قد اكتمل
            notifyListeners();
          }
        });
      }
    });
  }

  // دالة لإيقاف/تشغيل المؤقت
  void toggleTimer() {
    _isTimerPaused = !_isTimerPaused;
    notifyListeners();
  }

  // دالة لإعادة بدء السلسلة من السؤال الأول
  Future<void> restartSeries() async {
    debugPrint('تم استدعاء دالة restartSeries في LessonsQuizProvider');
    if (_currentSeries != null) {
      debugPrint('السلسلة الحالية: ${_currentSeries!.id}');

      // إيقاف الصوت والمؤقت
      _audioService.stopAudio();
      _cancelTimer();
      debugPrint('تم إيقاف الصوت والمؤقت');

      // إعادة تعيين حالة الاختبار
      _currentQuestionIndex = 0;
      _userAnswers = [];
      _selectedAnswerIndices = [];
      _status = LessonsQuizStatus.inProgress;
      debugPrint('تم إعادة تعيين حالة الاختبار');

      // تعيين المؤقت إلى قيمته الأولية
      _remainingTime = _timerDuration;

      // تعيين حالة المؤقت إلى متوقف
      _isTimerPaused = true;

      // إخطار المستمعين بالتغييرات
      notifyListeners();
      debugPrint('تم إخطار المستمعين بالتغييرات');

      // تأخير قصير قبل تشغيل الصوت
      await Future.delayed(const Duration(milliseconds: 300));

      // تشغيل الصوت وبدء المؤقت بعد انتهاء الصوت
      if (_status == LessonsQuizStatus.inProgress) {
        debugPrint('جاري تشغيل صوت السؤال الأول');
        await _playQuestionAudio();
        debugPrint('تم تشغيل صوت السؤال الأول');
      } else {
        debugPrint('تم تخطي تشغيل الصوت لأن حالة الاختبار ليست inProgress');
      }
    } else {
      debugPrint('خطأ: لا توجد سلسلة حالية');
    }
  }

  void _cancelTimer() {
    _timer?.cancel();
    _timer = null;
  }

  Future<void> _playQuestionAudio() async {
    if (currentQuestion != null) {
      try {
        debugPrint('تشغيل صوت السؤال: ${currentQuestion!.audioUrl}');

        // تسجيل دالة لبدء المؤقت بعد انتهاء تشغيل الصوت
        _audioService.onAudioComplete = () {
          debugPrint('انتهى تشغيل الصوت، بدء المؤقت');
          _isTimerPaused = false;
          _startTimer();
        };

        await _audioService.playAudio(currentQuestion!.audioUrl);
      } catch (e) {
        debugPrint('خطأ في تشغيل صوت السؤال: $e');

        // في حالة حدوث خطأ، نبدأ المؤقت مباشرة
        _isTimerPaused = false;
        _startTimer();
      }
    } else {
      debugPrint('لا يوجد سؤال حالي لتشغيل الصوت');

      // في حالة عدم وجود سؤال، نبدأ المؤقت مباشرة
      _isTimerPaused = false;
      _startTimer();
    }
  }

  Future<void> replayAudio() async {
    if (currentQuestion != null) {
      try {
        debugPrint('إعادة تشغيل صوت السؤال: ${currentQuestion!.audioUrl}');
        await _audioService.playAudio(currentQuestion!.audioUrl);
      } catch (e) {
        debugPrint('خطأ في إعادة تشغيل صوت السؤال: $e');
      }
    } else {
      debugPrint('لا يوجد سؤال حالي لإعادة تشغيل الصوت');
    }
  }

  Future<void> resetQuiz() async {
    debugPrint('تم استدعاء دالة resetQuiz');

    // إعادة تعيين حالة الاختبار
    _currentQuestionIndex = 0;
    _selectedAnswerIndices = [];
    _status = LessonsQuizStatus.initial;

    // إيقاف المؤقت والصوت
    _cancelTimer();
    _audioService.stopAudio();

    debugPrint('تم إعادة تعيين حالة الاختبار وإيقاف المؤقت والصوت');

    // إخطار المستمعين بالتغييرات
    notifyListeners();

    debugPrint('تم إخطار المستمعين بالتغييرات');
  }

  // دالة لتعيين إجابات المستخدم مباشرة (تستخدم في صفحة المراجعة)
  void setUserAnswers(List<UserAnswer> answers) {
    _userAnswers = answers;
    notifyListeners();
  }

  // دالة لتعيين حالة الاختبار مباشرة (تستخدم في صفحة المراجعة)
  void setQuizStatus(LessonsQuizStatus status) {
    _status = status;
    notifyListeners();
  }

  // دالة لتحميل السلسلة مباشرة بمعرفها (تستخدم في صفحة المراجعة)
  Future<Series?> loadSeriesById(int seriesId) async {
    try {
      // تحميل ملف السلسلة
      final seriesFile = await rootBundle
          .loadString('assets/lessons_series/series_$seriesId/questions.json');

      // تحويل البيانات إلى كائن Series
      final Map<String, dynamic> seriesData = jsonDecode(seriesFile);

      // إنشاء كائن Series يدويًا
      String title = '';
      String description = '';

      // تحديد العنوان والوصف بناءً على معرف السلسلة
      switch (seriesId) {
        case 1:
          title = 'إشارات المرور';
          description = 'تعلم إشارات المرور وقواعد السير';
          break;
        case 2:
          title = 'أنواع المركبات';
          description = 'تعرف على أنواع المركبات وخصائصها';
          break;
        case 3:
          title = 'المخالفات المرورية';
          description = 'تعرف على المخالفات المرورية وعقوباتها';
          break;
        default:
          title = 'سلسلة دروس $seriesId';
          description = 'دروس تعليمية للسياقة';
      }

      // إنشاء كائن Series
      final series = Series(
        id: seriesId,
        title: title,
        description: description,
        questions: (seriesData['questions'] as List)
            .map((q) => Question.fromJson(q))
            .toList(),
        isDownloaded: true,
        isComplete: true,
        totalQuestions: 40,
      );

      // تعيين السلسلة الحالية
      _currentSeries = series;

      // طباعة معلومات تشخيصية
      debugPrint('تم تحميل السلسلة: ${series.title}');
      debugPrint('عدد الأسئلة: ${series.questions.length}');

      // محاولة استعادة إجابات المستخدم من التخزين المحلي
      await _loadUserAnswers(seriesId);

      // إذا كانت الإجابات فارغة، نقوم بإنشاء إجابات افتراضية
      if (_userAnswers.isEmpty) {
        createEmptyAnswersForReview(seriesId);
      }

      notifyListeners();
      return series;
    } catch (e) {
      debugPrint('خطأ في تحميل السلسلة: $e');
      debugPrint('تفاصيل الخطأ: $e');
      return null;
    }
  }

  // دالة لإنشاء إجابات افتراضية غير صحيحة لجميع أسئلة السلسلة (للمراجعة بنتيجة 0)
  void createEmptyAnswersForReview(int seriesId) {
    try {
      if (_currentSeries == null) {
        debugPrint('لا توجد سلسلة حالية لإنشاء إجابات افتراضية');
        return;
      }

      debugPrint('إنشاء إجابات افتراضية للسلسلة $seriesId');

      // إنشاء إجابات افتراضية غير صحيحة لجميع الأسئلة
      _userAnswers = [];
      for (int i = 0; i < _currentSeries!.questions.length; i++) {
        final question = _currentSeries!.questions[i];

        // إنشاء إجابة خاطئة افتراضية (اختيار الإجابة الأولى دائمًا)
        List<int> wrongAnswerIndices = [0]; // اختيار الإجابة الأولى دائمًا
        bool isCorrect = false;

        // التحقق مما إذا كانت الإجابة الأولى هي الإجابة الصحيحة
        if (question.correctAnswerIndices.contains(1)) {
          // إذا كانت الإجابة الأولى صحيحة، نختار الإجابة الثانية
          wrongAnswerIndices = [1];
        }

        _userAnswers.add(UserAnswer(
          questionId: question.id,
          selectedAnswerIndices: wrongAnswerIndices,
          isCorrect: isCorrect,
        ));
      }

      // تعيين حالة الاختبار إلى مكتمل
      _status = LessonsQuizStatus.completed;

      debugPrint('تم إنشاء ${_userAnswers.length} إجابة افتراضية');

      // حفظ الإجابات في التخزين المحلي
      _saveUserAnswers();

      notifyListeners();
    } catch (e) {
      debugPrint('خطأ في إنشاء إجابات افتراضية للمراجعة: $e');
      _status = LessonsQuizStatus.initial;
      _userAnswers = [];
      notifyListeners();
    }
  }

  @override
  void dispose() {
    _cancelTimer();
    _audioService.dispose();
    _adService.dispose();
    super.dispose();
  }
}
