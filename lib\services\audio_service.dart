import 'package:audioplayers/audioplayers.dart';
import 'package:flutter/foundation.dart';
import 'dart:io';
import 'dart:async';

class AudioService {
  final AudioPlayer _audioPlayer = AudioPlayer();
  bool _isPlaying = false;

  // دالة للاستماع إلى انتهاء تشغيل الصوت
  Function? onAudioComplete;

  // الاستماع إلى حالة المشغل
  AudioService() {
    _audioPlayer.onPlayerStateChanged.listen((state) {
      debugPrint('حالة المشغل: $state');
      _isPlaying = state == PlayerState.playing;
    });

    _audioPlayer.onPlayerComplete.listen((event) {
      debugPrint('اكتمل تشغيل الملف الصوتي');
      _isPlaying = false;

      // استدعاء دالة الانتهاء إذا كانت مسجلة
      if (onAudioComplete != null) {
        final callback = onAudioComplete;
        onAudioComplete = null;
        callback!();
      }
    });
  }

  Future<void> playAudio(String audioPath) async {
    try {
      // إيقاف أي صوت قيد التشغيل
      await stopAudio();

      debugPrint('تشغيل الملف الصوتي: $audioPath');

      // التحقق من نوع المسار
      if (audioPath.startsWith('http')) {
        // مسار URL
        debugPrint('تشغيل كمسار URL: $audioPath');
        await _audioPlayer.play(UrlSource(audioPath));
      } else if (audioPath.startsWith('assets/')) {
        // مسار أصول
        String assetPath = audioPath.replaceFirst('assets/', '');
        debugPrint('تشغيل كمسار أصول: $assetPath');

        // تجربة تشغيل الصوت
        try {
          await _audioPlayer.play(AssetSource(assetPath));
          debugPrint('تم تشغيل الصوت بنجاح');
        } catch (innerError) {
          debugPrint('خطأ في تشغيل الصوت كأصل: $innerError');

          // محاولة بديلة
          try {
            await _audioPlayer.setSource(AssetSource(assetPath));
            await _audioPlayer.resume();
            debugPrint('تم تشغيل الصوت بالطريقة البديلة');
          } catch (alternativeError) {
            debugPrint('فشلت الطريقة البديلة أيضًا: $alternativeError');

            // استدعاء دالة الانتهاء في حالة الخطأ
            if (onAudioComplete != null) {
              final callback = onAudioComplete;
              onAudioComplete = null;
              callback!();
            }
          }
        }
      } else if (!kIsWeb && File(audioPath).existsSync()) {
        // مسار ملف محلي
        debugPrint('تشغيل كملف محلي: $audioPath');
        await _audioPlayer.play(DeviceFileSource(audioPath));
      } else {
        // محاولة تشغيل كمسار أصول
        debugPrint('محاولة تشغيل كمسار أصول: $audioPath');
        String assetPath = audioPath;
        if (audioPath.contains('/')) {
          assetPath = audioPath.split('/').last;
        }
        debugPrint('تشغيل كمسار أصول مبسط: $assetPath');
        await _audioPlayer.play(AssetSource(assetPath));
      }

      _isPlaying = true;
    } catch (e) {
      debugPrint('خطأ في تشغيل الملف الصوتي: $e');
      _isPlaying = false;

      // استدعاء دالة الانتهاء في حالة الخطأ
      if (onAudioComplete != null) {
        final callback = onAudioComplete;
        onAudioComplete = null;
        callback!();
      }
    }
  }

  Future<void> stopAudio() async {
    if (_isPlaying) {
      await _audioPlayer.stop();
      _isPlaying = false;
    }
  }

  // دالة لتحميل الصوت مسبقًا بدون تشغيله
  Future<void> preloadAudio(String audioPath) async {
    try {
      debugPrint('تحميل الملف الصوتي مسبقًا: $audioPath');

      // التحقق من نوع المسار
      if (audioPath.startsWith('http')) {
        // مسار URL
        debugPrint('تحميل مسبق كمسار URL: $audioPath');
        await _audioPlayer.setSource(UrlSource(audioPath));
      } else if (audioPath.startsWith('assets/')) {
        // مسار أصول
        String assetPath = audioPath.replaceFirst('assets/', '');
        debugPrint('تحميل مسبق كمسار أصول: $assetPath');

        try {
          await _audioPlayer.setSource(AssetSource(assetPath));
          debugPrint('تم تحميل الصوت مسبقًا بنجاح');
        } catch (innerError) {
          debugPrint('خطأ في تحميل الصوت مسبقًا كأصل: $innerError');

          // محاولة بديلة
          try {
            // محاولة تحميل بطريقة مختلفة
            await _audioPlayer.setSourceAsset(assetPath);
            debugPrint('تم تحميل الصوت مسبقًا بالطريقة البديلة');
          } catch (alternativeError) {
            debugPrint(
                'فشلت الطريقة البديلة للتحميل المسبق أيضًا: $alternativeError');
          }
        }
      } else if (!kIsWeb && File(audioPath).existsSync()) {
        // مسار ملف محلي
        debugPrint('تحميل مسبق كملف محلي: $audioPath');
        await _audioPlayer.setSource(DeviceFileSource(audioPath));
      } else {
        // محاولة تشغيل كمسار أصول
        debugPrint('محاولة تحميل مسبق كمسار أصول: $audioPath');
        String assetPath = audioPath;
        if (audioPath.contains('/')) {
          assetPath = audioPath.split('/').last;
        }
        debugPrint('تحميل مسبق كمسار أصول مبسط: $assetPath');
        await _audioPlayer.setSource(AssetSource(assetPath));
      }
    } catch (e) {
      debugPrint('خطأ في تحميل الملف الصوتي مسبقًا: $e');
    }
  }

  void dispose() {
    _audioPlayer.dispose();
  }
}
