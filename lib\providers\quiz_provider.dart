import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/series.dart';
import '../models/question.dart';
import '../models/user_answer.dart';
import '../services/series_data_service_new.dart';
import '../services/audio_service.dart';
import '../services/settings_service.dart';
import '../services/ad_service.dart';
import 'series_provider.dart';

enum QuizStatus { initial, inProgress, completed }

class QuizProvider with ChangeNotifier {
  final SeriesDataServiceNew _dataService = SeriesDataServiceNew();
  final AudioService _audioService = AudioService();
  final SettingsService _settingsService = SettingsService();
  final AdService _adService = AdService();

  List<Series> _seriesList = [];
  Series? _currentSeries;
  int _currentQuestionIndex = 0;
  List<UserAnswer> _userAnswers = [];
  List<int> _selectedAnswerIndices = [];
  QuizStatus _status = QuizStatus.initial;
  Timer? _timer;
  int _remainingTime = 30;
  int _timerDuration = 30;

  QuizProvider() {
    _loadSeries();
    _initializeAdService();
  }

  // تهيئة خدمة الإعلانات
  Future<void> _initializeAdService() async {
    // تخطي تهيئة الإعلانات إذا كان التطبيق يعمل على الويب
    if (kIsWeb) {
      debugPrint(
          'تم تخطي تهيئة الإعلانات في QuizProvider لأن التطبيق يعمل على الويب');
      return;
    }

    try {
      await _adService.initialize();
    } catch (e) {
      debugPrint('فشل في تهيئة خدمة الإعلانات في QuizProvider: $e');
    }
  }

  List<Series> get seriesList => _seriesList;
  Series? get currentSeries => _currentSeries;
  Question? get currentQuestion => _currentSeries != null &&
          _currentQuestionIndex < _currentSeries!.questions.length
      ? _currentSeries!.questions[_currentQuestionIndex]
      : null;
  int get currentQuestionIndex => _currentQuestionIndex;
  // الحصول على إجابات المستخدم مرتبة حسب معرف السؤال
  List<UserAnswer> get userAnswers {
    // نسخة من الإجابات مرتبة حسب معرف السؤال
    final sortedAnswers = List<UserAnswer>.from(_userAnswers);
    sortedAnswers.sort((a, b) => a.questionId.compareTo(b.questionId));
    return sortedAnswers;
  }

  List<int> get selectedAnswerIndices => _selectedAnswerIndices;
  QuizStatus get status => _status;
  int get remainingTime => _remainingTime;
  int get timerDuration => _timerDuration;

  int get totalQuestions => _currentSeries?.questions.length ?? 0;
  int get correctAnswersCount =>
      _userAnswers.where((answer) => answer.isCorrect).length;

  Future<void> _loadSeries() async {
    try {
      // تحميل السلاسل
      _seriesList = await _dataService.getSeries();

      // تحميل إعدادات المؤقت
      final settings = await _settingsService.getSettings();
      _timerDuration = settings.timerDuration;

      notifyListeners();
    } catch (e) {
      debugPrint('خطأ في تحميل السلاسل أو الإعدادات: $e');
      _seriesList = [];
      notifyListeners();
    }
  }

  // دالة لتحميل بيانات السلسلة مسبقًا بدون بدء الاختبار
  Future<void> preloadSeries(Series series) async {
    // تعيين السلسلة الحالية فقط
    _currentSeries = series;
    _currentQuestionIndex = 0;

    // تحميل الصوت مسبقًا
    if (currentQuestion != null) {
      // تم إزالة المعالجة الخاصة للسلسلة 4

      // تهيئة الصوت مسبقًا (بدون تشغيله)
      try {
        await _audioService.preloadAudio(currentQuestion!.audioUrl);
      } catch (e) {
        debugPrint('خطأ في تحميل الصوت مسبقًا: $e');
      }
    }
  }

  Future<void> startSeries(Series series) async {
    // تعيين حالة البدء قبل تحميل الصوت
    _currentSeries = series;
    _currentQuestionIndex = 0;
    _userAnswers = [];
    _selectedAnswerIndices = [];
    _status = QuizStatus.inProgress;

    // تعيين المؤقت إلى قيمته الأولية
    _remainingTime = _timerDuration;

    // تعيين حالة المؤقت إلى متوقف
    _isTimerPaused = true;

    // تحميل الإعلان مسبقًا في الخلفية
    _preloadAd();

    // إخطار المستمعين بالتغييرات الأولية
    notifyListeners();

    // تشغيل الصوت وبدء المؤقت بعد انتهاء الصوت
    await _playQuestionAudio();
  }

  // تحميل الإعلان مسبقًا
  void _preloadAd() {
    // تخطي تحميل الإعلان إذا كان التطبيق يعمل على الويب
    if (kIsWeb) {
      debugPrint('تم تخطي تحميل الإعلان مسبقًا لأن التطبيق يعمل على الويب');
      return;
    }

    debugPrint('جاري تحميل الإعلان مسبقًا...');
    _adService.loadInterstitialAd();
  }

  // دالة لبدء الاختبار باستخدام معرف السلسلة
  Future<void> startQuiz(int seriesId) async {
    try {
      // البحث عن السلسلة
      final series = _seriesList.firstWhere(
        (s) => s.id == seriesId,
        orElse: () => _seriesList.isNotEmpty
            ? _seriesList.first
            : Series(
                id: 1,
                title: 'سلسلة افتراضية',
                questions: [],
              ),
      );

      // بدء الاختبار بالسلسلة
      await startSeries(series);
    } catch (e) {
      debugPrint('خطأ في بدء اختبار السلسلة $seriesId: $e');
    }
  }

  void selectAnswer(int answerIndex) {
    if (_status != QuizStatus.inProgress) return;

    // استخدام المؤشر كما هو (يبدأ من 0 في الكود، ولكن يمثل 1 في واجهة المستخدم)
    if (_selectedAnswerIndices.contains(answerIndex)) {
      _selectedAnswerIndices.remove(answerIndex);
    } else {
      _selectedAnswerIndices.add(answerIndex);
    }
    notifyListeners();
  }

  void clearSelectedAnswers() {
    _selectedAnswerIndices = [];
    notifyListeners();
  }

  Future<void> submitAnswer() async {
    if (_status != QuizStatus.inProgress || currentQuestion == null) return;

    final question = currentQuestion!;
    final isCorrect = _areAnswersCorrect(
        _selectedAnswerIndices, question.correctAnswerIndices);

    _userAnswers.add(UserAnswer(
      questionId: question.id,
      selectedAnswerIndices: List.from(_selectedAnswerIndices),
      isCorrect: isCorrect,
    ));

    _cancelTimer();

    if (_currentQuestionIndex < _currentSeries!.questions.length - 1) {
      await _goToNextQuestion();
    } else {
      _completeQuiz();
    }
  }

  bool _areAnswersCorrect(List<int> selectedIndices, List<int> correctIndices) {
    if (selectedIndices.length != correctIndices.length) return false;

    // تحويل المؤشرات المحددة إلى مؤشرات تبدأ من 1
    List<int> selectedIndicesFrom1 =
        selectedIndices.map((index) => index + 1).toList();

    // مقارنة المؤشرات المحددة مع المؤشرات الصحيحة
    for (final index in correctIndices) {
      if (!selectedIndicesFrom1.contains(index)) return false;
    }

    for (final index in selectedIndicesFrom1) {
      if (!correctIndices.contains(index)) return false;
    }

    return true;
  }

  Future<void> _goToNextQuestion() async {
    _currentQuestionIndex++;
    _selectedAnswerIndices = [];

    // إيقاف المؤقت مؤقتًا
    _cancelTimer();

    // تعيين المؤقت إلى قيمته الأولية
    _remainingTime = _timerDuration;

    // تعيين حالة المؤقت إلى متوقف
    _isTimerPaused = true;

    // إخطار المستمعين بالتغييرات
    notifyListeners();

    // تشغيل الصوت وبدء المؤقت بعد انتهاء الصوت
    await _playQuestionAudio();
  }

  void _completeQuiz() {
    _status = QuizStatus.completed;
    _audioService.stopAudio();

    // تحديث نتيجة الاختبار في SeriesProvider
    if (_currentSeries != null) {
      // حفظ إجابات المستخدم في التخزين المحلي
      _saveUserAnswers();

      // عرض الإعلان إذا كان جاهزًا
      _showAdIfReady();

      // يمكن الوصول إلى SeriesProvider من خلال BuildContext
      // سيتم استدعاء هذه الدالة من خارج هذا الكلاس
      notifyListeners();
    }
  }

  // عرض الإعلان إذا كان جاهزًا
  Future<void> _showAdIfReady() async {
    // تخطي عرض الإعلان إذا كان التطبيق يعمل على الويب
    if (kIsWeb) {
      debugPrint('تم تخطي عرض الإعلان لأن التطبيق يعمل على الويب');
      return;
    }

    if (_adService.isInterstitialAdReady) {
      debugPrint('عرض الإعلان البيني بعد اكتمال السلسلة');
      await _adService.showInterstitialAd();
    } else {
      debugPrint('الإعلان غير جاهز للعرض بعد اكتمال السلسلة');
    }
  }

  // حفظ إجابات المستخدم في التخزين المحلي
  Future<void> _saveUserAnswers() async {
    if (_currentSeries == null) return;

    try {
      final prefs = await SharedPreferences.getInstance();

      // تحويل إجابات المستخدم إلى قائمة من الخرائط
      final userAnswersJson =
          _userAnswers.map((answer) => answer.toJson()).toList();

      // حفظ إجابات المستخدم كسلسلة JSON
      await prefs.setString(
        'user_answers_${_currentSeries!.id}',
        jsonEncode(userAnswersJson),
      );

      // حفظ حالة الاختبار
      await prefs.setInt('quiz_status_${_currentSeries!.id}', _status.index);

      debugPrint('تم حفظ إجابات المستخدم للسلسلة ${_currentSeries!.id}');
    } catch (e) {
      debugPrint('خطأ في حفظ إجابات المستخدم: $e');
    }
  }

  // استعادة إجابات المستخدم من التخزين المحلي
  Future<void> loadUserAnswers(int seriesId) async {
    try {
      // البحث عن السلسلة المطلوبة
      final series = _seriesList.firstWhere(
        (s) => s.id == seriesId,
        orElse: () => throw Exception('السلسلة غير موجودة'),
      );

      // تعيين السلسلة الحالية
      _currentSeries = series;

      final prefs = await SharedPreferences.getInstance();

      // استعادة حالة الاختبار
      final statusIndex = prefs.getInt('quiz_status_$seriesId');
      if (statusIndex != null) {
        _status = QuizStatus.values[statusIndex];
      } else {
        _status = QuizStatus.initial;
      }

      // استعادة إجابات المستخدم
      final userAnswersJson = prefs.getString('user_answers_$seriesId');
      if (userAnswersJson != null && userAnswersJson.isNotEmpty) {
        final List<dynamic> answersData = jsonDecode(userAnswersJson);
        _userAnswers =
            answersData.map((data) => UserAnswer.fromJson(data)).toList();

        debugPrint('تم استعادة ${_userAnswers.length} إجابة للسلسلة $seriesId');
      } else {
        // إذا لم تكن هناك إجابات محفوظة، نقوم بإنشاء إجابات افتراضية غير صحيحة
        createEmptyAnswersForReview(seriesId);
        debugPrint('تم إنشاء إجابات افتراضية غير صحيحة للسلسلة $seriesId');
        return; // نخرج من الدالة لأن createEmptyAnswersForReview تقوم بالفعل بإخطار المستمعين
      }

      notifyListeners();
    } catch (e) {
      debugPrint('خطأ في استعادة إجابات المستخدم: $e');
      _status = QuizStatus.initial;
      _userAnswers = [];
      notifyListeners();
    }
  }

  // دالة لتحديث نتيجة الاختبار في SeriesProvider
  void updateScoreInSeriesProvider(SeriesProvider seriesProvider) {
    if (_currentSeries != null) {
      seriesProvider.updateLastScore(_currentSeries!.id, correctAnswersCount);
    }
  }

  // متغير لتتبع حالة المؤقت
  bool _isTimerPaused = false;
  bool get isTimerPaused => _isTimerPaused;

  void _startTimer() {
    _remainingTime = _timerDuration;
    _cancelTimer();
    _isTimerPaused = false;

    _timer = Timer.periodic(const Duration(seconds: 1), (timer) async {
      if (_remainingTime > 0) {
        if (!_isTimerPaused) {
          _remainingTime--;
          notifyListeners();
        }
      } else {
        await submitAnswer();

        // إضافة تأخير صغير للتأكد من أن حالة الاختبار تم تحديثها
        Future.delayed(const Duration(milliseconds: 100), () {
          if (_status == QuizStatus.completed) {
            // إخطار المستمعين بأن الاختبار قد اكتمل
            notifyListeners();
          }
        });
      }
    });
  }

  // دالة لإيقاف/تشغيل المؤقت
  void toggleTimer() {
    _isTimerPaused = !_isTimerPaused;
    notifyListeners();
  }

  // دالة لإعادة بدء السلسلة من السؤال الأول
  Future<void> restartSeries() async {
    if (_currentSeries != null) {
      _audioService.stopAudio();
      _cancelTimer();
      _currentQuestionIndex = 0;
      _userAnswers = [];
      _selectedAnswerIndices = [];
      _status = QuizStatus.inProgress;

      // تعيين المؤقت إلى قيمته الأولية
      _remainingTime = _timerDuration;

      // تعيين حالة المؤقت إلى متوقف
      _isTimerPaused = true;

      // إخطار المستمعين بالتغييرات
      notifyListeners();

      // تشغيل الصوت وبدء المؤقت بعد انتهاء الصوت
      await _playQuestionAudio();
    }
  }

  void _cancelTimer() {
    _timer?.cancel();
    _timer = null;
  }

  void setTimerDuration(int seconds) {
    _timerDuration = seconds;

    // إذا كان الاختبار قيد التقدم، نقوم بإعادة تعيين المؤقت
    if (_status == QuizStatus.inProgress) {
      _cancelTimer();
      _startTimer();
    }

    notifyListeners();
  }

  Future<void> _playQuestionAudio() async {
    if (currentQuestion != null) {
      try {
        debugPrint('تشغيل صوت السؤال: ${currentQuestion!.audioUrl}');

        // تسجيل دالة لبدء المؤقت بعد انتهاء تشغيل الصوت
        _audioService.onAudioComplete = () {
          debugPrint('انتهى تشغيل الصوت، بدء المؤقت');
          _isTimerPaused = false;
          _startTimer();
        };

        await _audioService.playAudio(currentQuestion!.audioUrl);
      } catch (e) {
        debugPrint('خطأ في تشغيل صوت السؤال: $e');

        // في حالة حدوث خطأ، نبدأ المؤقت مباشرة
        _isTimerPaused = false;
        _startTimer();
      }
    } else {
      debugPrint('لا يوجد سؤال حالي لتشغيل الصوت');

      // في حالة عدم وجود سؤال، نبدأ المؤقت مباشرة
      _isTimerPaused = false;
      _startTimer();
    }
  }

  Future<void> replayAudio() async {
    if (currentQuestion != null) {
      try {
        debugPrint('إعادة تشغيل صوت السؤال: ${currentQuestion!.audioUrl}');
        await _audioService.playAudio(currentQuestion!.audioUrl);
      } catch (e) {
        debugPrint('خطأ في إعادة تشغيل صوت السؤال: $e');
      }
    } else {
      debugPrint('لا يوجد سؤال حالي لإعادة تشغيل الصوت');
    }
  }

  // دالة لإكمال الاختبار مباشرة للمراجعة
  void completeQuizForReview() {
    if (_currentSeries != null) {
      _cancelTimer();
      _audioService.stopAudio();

      // إنشاء إجابات افتراضية لجميع الأسئلة
      _userAnswers = [];
      for (int i = 0; i < _currentSeries!.questions.length; i++) {
        final question = _currentSeries!.questions[i];

        // إنشاء إجابة صحيحة افتراضية
        _userAnswers.add(UserAnswer(
          questionId: question.id,
          selectedAnswerIndices:
              question.correctAnswerIndices.map((index) => index - 1).toList(),
          isCorrect: true,
        ));
      }

      // تعيين حالة الاختبار إلى مكتمل
      _status = QuizStatus.completed;
      notifyListeners();
    }
  }

  // دالة لإنشاء إجابات افتراضية غير صحيحة لجميع أسئلة السلسلة (للمراجعة بنتيجة 0)
  void createEmptyAnswersForReview(int seriesId) {
    try {
      // البحث عن السلسلة المطلوبة
      final series = _seriesList.firstWhere(
        (s) => s.id == seriesId,
        orElse: () => throw Exception('السلسلة غير موجودة'),
      );

      // تعيين السلسلة الحالية
      _currentSeries = series;

      // إنشاء إجابات افتراضية غير صحيحة لجميع الأسئلة
      _userAnswers = [];
      for (int i = 0; i < series.questions.length; i++) {
        final question = series.questions[i];

        // إنشاء إجابة خاطئة افتراضية (اختيار الإجابة الأولى دائمًا)
        List<int> wrongAnswerIndices = [0]; // اختيار الإجابة الأولى دائمًا
        bool isCorrect = false;

        // التحقق مما إذا كانت الإجابة الأولى هي الإجابة الصحيحة
        if (question.correctAnswerIndices.contains(1)) {
          // إذا كانت الإجابة الأولى صحيحة، نختار الإجابة الثانية
          wrongAnswerIndices = [1];
        }

        _userAnswers.add(UserAnswer(
          questionId: question.id,
          selectedAnswerIndices: wrongAnswerIndices,
          isCorrect: isCorrect,
        ));
      }

      // تعيين حالة الاختبار إلى مكتمل
      _status = QuizStatus.completed;
      notifyListeners();

      // حفظ الإجابات في التخزين المحلي
      _saveUserAnswers();
    } catch (e) {
      debugPrint('خطأ في إنشاء إجابات افتراضية للمراجعة: $e');
      _status = QuizStatus.initial;
      _userAnswers = [];
      notifyListeners();
    }
  }

  // تم حذف الدالة المكررة

  // دالة لمحاكاة إكمال الاختبار مع نتيجة محددة
  void simulateQuizCompletion(int targetScore) {
    if (_currentSeries == null) return;

    // إيقاف الصوت والمؤقت
    _cancelTimer();
    _audioService.stopAudio();

    // تعيين حالة الاختبار إلى مكتمل
    _status = QuizStatus.completed;

    // إنشاء إجابات افتراضية تعكس النتيجة المستهدفة
    _userAnswers = [];

    // عدد الإجابات الصحيحة التي نريد إنشاءها
    int correctAnswersToCreate = targetScore;

    // إذا كانت النتيجة المستهدفة أكبر من عدد الأسئلة، نقوم بتصحيحها
    if (correctAnswersToCreate > _currentSeries!.questions.length) {
      correctAnswersToCreate = _currentSeries!.questions.length;
    }

    // إذا كانت النتيجة المستهدفة صفر ولكن هناك أسئلة، نجعلها على الأقل 1
    if (correctAnswersToCreate == 0 && _currentSeries!.questions.isNotEmpty) {
      correctAnswersToCreate = 1;
    }

    // إنشاء قائمة بمؤشرات الأسئلة
    List<int> questionIndices =
        List.generate(_currentSeries!.questions.length, (index) => index);

    // خلط مؤشرات الأسئلة لجعل الإجابات الصحيحة والخاطئة موزعة بشكل عشوائي
    questionIndices.shuffle();

    // تحديد الأسئلة التي ستكون إجاباتها صحيحة
    List<int> correctIndices =
        questionIndices.sublist(0, correctAnswersToCreate);

    // إنشاء إجابات لجميع الأسئلة
    for (int i = 0; i < _currentSeries!.questions.length; i++) {
      final question = _currentSeries!.questions[i];

      // إذا كان هذا السؤال في قائمة الإجابات الصحيحة
      if (correctIndices.contains(i)) {
        _userAnswers.add(UserAnswer(
          questionId: question.id,
          selectedAnswerIndices:
              question.correctAnswerIndices.map((index) => index - 1).toList(),
          isCorrect: true,
        ));
      }
      // وإلا، نقوم بإنشاء إجابة خاطئة
      else {
        // إنشاء إجابة خاطئة بطريقة أكثر واقعية
        List<int> wrongAnswerIndices = [];

        // إذا كان هناك إجابة واحدة صحيحة فقط
        if (question.correctAnswerIndices.length == 1) {
          // اختيار إجابة خاطئة عشوائية
          int correctIndex = question.correctAnswerIndices[0] -
              1; // تحويل من 1-based إلى 0-based
          int totalAnswers = question.answers.length;

          if (totalAnswers > 1) {
            // اختيار إجابة مختلفة عن الإجابة الصحيحة
            int wrongIndex = (correctIndex + 1) % totalAnswers;
            wrongAnswerIndices.add(wrongIndex);
          } else {
            // إذا كان هناك إجابة واحدة فقط، نترك القائمة فارغة
            wrongAnswerIndices = [];
          }
        }
        // إذا كان هناك أكثر من إجابة صحيحة
        else if (question.correctAnswerIndices.length > 1) {
          // اختيار بعض الإجابات الصحيحة فقط (ليست كلها)
          wrongAnswerIndices = question.correctAnswerIndices
              .sublist(0, question.correctAnswerIndices.length - 1)
              .map((index) => index - 1)
              .toList();
        }

        _userAnswers.add(UserAnswer(
          questionId: question.id,
          selectedAnswerIndices: wrongAnswerIndices,
          isCorrect: false,
        ));
      }
    }

    notifyListeners();
  }

  Future<void> resetQuiz() async {
    // حفظ معرف السلسلة الحالية قبل إعادة التعيين
    final currentSeriesId = _currentSeries?.id;

    // حفظ إجابات المستخدم قبل إعادة التعيين
    final savedUserAnswers = List<UserAnswer>.from(_userAnswers);

    // إعادة تعيين حالة الاختبار
    _currentQuestionIndex = 0;
    _selectedAnswerIndices = [];
    _status = QuizStatus.initial;
    _cancelTimer();
    _audioService.stopAudio();

    // الاحتفاظ بالسلسلة الحالية وإجابات المستخدم
    if (currentSeriesId != null) {
      // لا نحذف البيانات المحفوظة للسلسلة الحالية
      debugPrint(
          'تم إعادة تعيين الاختبار مع الاحتفاظ بالنتائج للسلسلة $currentSeriesId');

      // استعادة إجابات المستخدم
      _userAnswers = savedUserAnswers;
    }

    notifyListeners();
  }

  @override
  void dispose() {
    _cancelTimer();
    _audioService.dispose();
    _adService.dispose();
    super.dispose();
  }
}
