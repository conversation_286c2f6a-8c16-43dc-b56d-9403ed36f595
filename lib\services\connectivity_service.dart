import 'dart:async';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/material.dart';

class ConnectivityService {
  // مثيل وحيد من الخدمة (Singleton)
  static final ConnectivityService _instance = ConnectivityService._internal();
  factory ConnectivityService() => _instance;
  ConnectivityService._internal();

  // مثيل من Connectivity
  final Connectivity _connectivity = Connectivity();

  // متغير لتخزين حالة الاتصال الحالية
  bool _isConnected = false;

  // متغير لتخزين مستمع التغييرات
  StreamSubscription<ConnectivityResult>? _connectivitySubscription;

  // دالة للحصول على حالة الاتصال الحالية
  bool get isConnected => _isConnected;

  // دالة للتحقق من الاتصال بالإنترنت
  Future<bool> checkConnectivity() async {
    final result = await _connectivity.checkConnectivity();
    _isConnected = result != ConnectivityResult.none;
    return _isConnected;
  }

  // دالة لبدء مراقبة حالة الاتصال
  void startMonitoring(BuildContext context) {
    _connectivitySubscription =
        _connectivity.onConnectivityChanged.listen((result) {
      final wasConnected = _isConnected;
      _isConnected = result != ConnectivityResult.none;

      // إذا تغيرت حالة الاتصال من متصل إلى غير متصل
      if (wasConnected && !_isConnected && context.mounted) {
        _showNoInternetDialog(context);
      }
    });
  }

  // دالة لإيقاف مراقبة حالة الاتصال
  void stopMonitoring() {
    _connectivitySubscription?.cancel();
    _connectivitySubscription = null;
  }

  // دالة لعرض رسالة عدم وجود اتصال بالإنترنت
  void _showNoInternetDialog(BuildContext context) {
    if (context.mounted) {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => PopScope(
          canPop: false, // منع إغلاق الرسالة بالضغط على زر الرجوع
          child: AlertDialog(
            title: const Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.signal_wifi_off,
                  color: Colors.red,
                  size: 30,
                ),
                SizedBox(width: 10),
                Text(
                  'لا يوجد اتصال بالإنترنت',
                  style: TextStyle(fontFamily: 'Cairo'),
                ),
              ],
            ),
            content: const Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.wifi_off,
                  color: Colors.red,
                  size: 60,
                ),
                SizedBox(height: 16),
                Text(
                  'يرجى التحقق من اتصال الإنترنت الخاص بك والمحاولة مرة أخرى. التطبيق يتطلب اتصالاً بالإنترنت للعمل بشكل صحيح.',
                  style: TextStyle(fontFamily: 'Cairo'),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () async {
                  final isNowConnected = await checkConnectivity();
                  if (context.mounted) {
                    if (isNowConnected) {
                      Navigator.pop(context);
                    } else {
                      // إظهار رسالة أن الاتصال لا يزال مقطوعًا
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text(
                            'لا يزال الاتصال بالإنترنت مقطوعًا. يرجى المحاولة مرة أخرى.',
                            style: TextStyle(fontFamily: 'Cairo'),
                          ),
                          duration: Duration(seconds: 2),
                        ),
                      );
                    }
                  }
                },
                child: const Text('إعادة المحاولة',
                    style: TextStyle(fontFamily: 'Cairo')),
              ),
            ],
          ),
        ),
      );
    }
  }

  // دالة لعرض رسالة عدم وجود اتصال بالإنترنت عند بدء التطبيق
  Future<void> showInitialConnectionDialog(BuildContext context) async {
    final isConnected = await checkConnectivity();
    if (!isConnected && context.mounted) {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => PopScope(
          canPop: false, // منع إغلاق الرسالة بالضغط على زر الرجوع
          child: AlertDialog(
            title: const Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.signal_wifi_off,
                  color: Colors.red,
                  size: 30,
                ),
                SizedBox(width: 10),
                Text(
                  'لا يوجد اتصال بالإنترنت',
                  style: TextStyle(fontFamily: 'Cairo'),
                ),
              ],
            ),
            content: const Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.wifi_off,
                  color: Colors.red,
                  size: 60,
                ),
                SizedBox(height: 16),
                Text(
                  'يرجى التحقق من اتصال الإنترنت الخاص بك والمحاولة مرة أخرى. التطبيق يتطلب اتصالاً بالإنترنت للعمل بشكل صحيح.',
                  style: TextStyle(fontFamily: 'Cairo'),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () async {
                  final isNowConnected = await checkConnectivity();
                  if (context.mounted) {
                    if (isNowConnected) {
                      Navigator.pop(context);
                    } else {
                      // إظهار رسالة أن الاتصال لا يزال مقطوعًا
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text(
                            'لا يزال الاتصال بالإنترنت مقطوعًا. يرجى المحاولة مرة أخرى.',
                            style: TextStyle(fontFamily: 'Cairo'),
                          ),
                          duration: Duration(seconds: 2),
                        ),
                      );
                    }
                  }
                },
                child: const Text('إعادة المحاولة',
                    style: TextStyle(fontFamily: 'Cairo')),
              ),
            ],
          ),
        ),
      );
    }
  }
}
