import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'dart:typed_data';
import '../providers/quiz_provider.dart';
import 'review_screen.dart';

// صورة شفافة صغيرة للاستخدام كمؤشر تحميل
final Uint8List kTransparentImage = Uint8List.fromList([
  0x89,
  0x50,
  0x4E,
  0x47,
  0x0D,
  0x0A,
  0x1A,
  0x0A,
  0x00,
  0x00,
  0x00,
  0x0D,
  0x49,
  0x48,
  0x44,
  0x52,
  0x00,
  0x00,
  0x00,
  0x01,
  0x00,
  0x00,
  0x00,
  0x01,
  0x08,
  0x06,
  0x00,
  0x00,
  0x00,
  0x1F,
  0x15,
  0xC4,
  0x89,
  0x00,
  0x00,
  0x00,
  0x0A,
  0x49,
  0x44,
  0x41,
  0x54,
  0x78,
  0x9C,
  0x63,
  0x00,
  0x01,
  0x00,
  0x00,
  0x05,
  0x00,
  0x01,
  0x0D,
  0x0A,
  0x2D,
  0xB4,
  0x00,
  0x00,
  0x00,
  0x00,
  0x49,
  0x45,
  0x4E,
  0x44,
  0xAE,
  0x42,
  0x60,
  0x82
]);

class QuestionScreenNew extends StatefulWidget {
  const QuestionScreenNew({Key? key}) : super(key: key);

  @override
  State<QuestionScreenNew> createState() => _QuestionScreenNewState();
}

class _QuestionScreenNewState extends State<QuestionScreenNew> {
  // دالة للتحقق من اتجاه الشاشة (أفقي أو عمودي)
  bool isLandscape(BuildContext context) {
    return MediaQuery.of(context).orientation == Orientation.landscape;
  }

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    // التخلص من جميع وحدات التحكم عند إنهاء الشاشة
    for (var controller in _transformationControllers.values) {
      controller.dispose();
    }
    _transformationControllers.clear();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) async {
        if (didPop) return;

        // عرض رسالة تأكيد
        final shouldPop = await showDialog<bool>(
              context: context,
              builder: (context) => AlertDialog(
                title: const Text('إلغاء الاختبار',
                    style: TextStyle(fontFamily: 'Cairo')),
                content: const Text('هل أنت متأكد من إلغاء الاختبار؟',
                    style: TextStyle(fontFamily: 'Cairo')),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.pop(context, false),
                    child:
                        const Text('لا', style: TextStyle(fontFamily: 'Cairo')),
                  ),
                  TextButton(
                    onPressed: () {
                      final quizProvider =
                          Provider.of<QuizProvider>(context, listen: false);
                      quizProvider.resetQuiz();
                      Navigator.pop(context, true);
                    },
                    child: const Text('نعم',
                        style: TextStyle(fontFamily: 'Cairo')),
                  ),
                ],
              ),
            ) ??
            false;

        if (shouldPop && context.mounted) {
          Navigator.of(context).pop();
        }
      },
      child: Scaffold(
        backgroundColor: const Color(0xFF1A1B26),
        appBar: isLandscape(context)
            ? null // إخفاء AppBar في الوضع الأفقي
            : AppBar(
                backgroundColor:
                    const Color(0xFF0D47A1), // لون أزرق غامق مطابق للسلاسل
                title: Center(
                  child: Image.asset(
                    'assets/images/logo.png',
                    width: 40,
                    height: 40,
                    fit: BoxFit.contain,
                  ),
                ),
                actions: [
                  // زر إعادة تشغيل السلسلة
                  IconButton(
                    icon: const Icon(Icons.restart_alt, color: Colors.white),
                    tooltip: 'إعادة بدء السلسلة',
                    onPressed: () {
                      // إعادة بدء السلسلة من السؤال الأول
                      showDialog(
                        context: context,
                        builder: (context) => AlertDialog(
                          title: const Text('إعادة بدء السلسلة',
                              style: TextStyle(fontFamily: 'Cairo')),
                          content: const Text(
                              'هل أنت متأكد من إعادة بدء السلسلة من السؤال الأول؟',
                              style: TextStyle(fontFamily: 'Cairo')),
                          actions: [
                            TextButton(
                              onPressed: () => Navigator.pop(context),
                              child: const Text('لا',
                                  style: TextStyle(fontFamily: 'Cairo')),
                            ),
                            TextButton(
                              onPressed: () {
                                final quizProvider = Provider.of<QuizProvider>(
                                    context,
                                    listen: false);
                                quizProvider.restartSeries();
                                Navigator.pop(context); // إغلاق الحوار
                              },
                              child: const Text('نعم',
                                  style: TextStyle(fontFamily: 'Cairo')),
                            ),
                          ],
                        ),
                      );
                    },
                  ),
                  // زر العودة للصفحة الرئيسية
                  IconButton(
                    icon: const Icon(Icons.home, color: Colors.white),
                    onPressed: () {
                      showDialog(
                        context: context,
                        builder: (context) => AlertDialog(
                          title: const Text('إلغاء الاختبار',
                              style: TextStyle(fontFamily: 'Cairo')),
                          content: const Text('هل أنت متأكد من إلغاء الاختبار؟',
                              style: TextStyle(fontFamily: 'Cairo')),
                          actions: [
                            TextButton(
                              onPressed: () => Navigator.pop(context),
                              child: const Text('لا',
                                  style: TextStyle(fontFamily: 'Cairo')),
                            ),
                            TextButton(
                              onPressed: () {
                                final quizProvider = Provider.of<QuizProvider>(
                                    context,
                                    listen: false);
                                quizProvider.resetQuiz();
                                Navigator.pop(context); // إغلاق الحوار
                                Navigator.pop(
                                    context); // العودة إلى شاشة السلاسل
                              },
                              child: const Text('نعم',
                                  style: TextStyle(fontFamily: 'Cairo')),
                            ),
                          ],
                        ),
                      );
                    },
                  ),
                ],
                leading: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // زر إيقاف/تشغيل المؤقت
                    Consumer<QuizProvider>(
                      builder: (context, quizProvider, child) {
                        return IconButton(
                          icon: Icon(
                            quizProvider.isTimerPaused
                                ? Icons.play_arrow
                                : Icons.pause,
                            color: Colors.white,
                            size: 28, // زيادة حجم الأيقونة
                          ),
                          tooltip: quizProvider.isTimerPaused
                              ? 'استئناف المؤقت'
                              : 'إيقاف المؤقت',
                          onPressed: () {
                            // إيقاف/تشغيل المؤقت
                            quizProvider.toggleTimer();
                          },
                          padding: EdgeInsets.zero,
                          constraints: const BoxConstraints(),
                        );
                      },
                    ),
                    // زر إعادة تشغيل الصوت
                    Consumer<QuizProvider>(
                      builder: (context, quizProvider, child) {
                        return IconButton(
                          icon: const Icon(
                            Icons.volume_up,
                            color: Colors.white,
                            size: 28,
                          ),
                          tooltip: 'إعادة تشغيل الصوت',
                          onPressed: () {
                            quizProvider.replayAudio();
                          },
                          padding: EdgeInsets.zero,
                          constraints: const BoxConstraints(),
                        );
                      },
                    ),
                  ],
                ),
                automaticallyImplyLeading: false,
                leadingWidth: 96, // زيادة العرض لاستيعاب الزرين
              ),
        body: Consumer<QuizProvider>(
          builder: (context, quizProvider, child) {
            // التحقق من حالة الاختبار
            if (quizProvider.status == QuizStatus.completed) {
              // إذا كان الاختبار مكتمل، ننتقل إلى صفحة المراجعة
              WidgetsBinding.instance.addPostFrameCallback((_) {
                Navigator.pushReplacement(
                  context,
                  MaterialPageRoute(builder: (context) => const ReviewScreen()),
                );
              });
            }

            final question = quizProvider.currentQuestion;

            if (question == null) {
              return const Center(
                child: CircularProgressIndicator(color: Color(0xFF0D47A1)),
              );
            }

            // استخدام تصميم مختلف حسب اتجاه الشاشة
            if (isLandscape(context)) {
              return _buildLandscapeLayout(context, quizProvider, question);
            } else {
              return _buildPortraitLayout(context, quizProvider, question);
            }
          },
        ),
      ),
    );
  }

  // تصميم الوضع العمودي (الحالي)
  Widget _buildPortraitLayout(
      BuildContext context, QuizProvider quizProvider, dynamic question) {
    return Column(
      children: [
        // عنوان السؤال والعداد
        Container(
          color: Colors.black,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // رقم السؤال
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.grey[800],
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Text(
                  '${quizProvider.currentQuestionIndex + 1}/${quizProvider.totalQuestions}',
                  style: const TextStyle(
                    fontFamily: 'Cairo',
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              // رقم السلسلة
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.grey[800],
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Text(
                  'السلسلة ${quizProvider.currentSeries?.id ?? 1}',
                  style: const TextStyle(
                    fontFamily: 'Cairo',
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              // المؤقت
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.grey[800],
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Row(
                  children: [
                    Icon(quizProvider.isTimerPaused ? Icons.pause : Icons.timer,
                        color: Colors.white, size: 16),
                    const SizedBox(width: 4),
                    Text(
                      '${quizProvider.remainingTime}s',
                      style: TextStyle(
                        fontFamily: 'Cairo',
                        color: quizProvider.isTimerPaused
                            ? Colors.yellow
                            : (quizProvider.remainingTime < 5
                                ? Colors.red
                                : Colors.white),
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),

        // صورة السؤال
        Expanded(
          flex: 6, // زيادة نسبة الصورة
          child: Container(
            width: double.infinity,
            decoration: const BoxDecoration(
              color: Colors.black,
            ),
            child: Stack(
              fit: StackFit.expand,
              children: [
                // صورة السؤال - استخدام صورة من الإنترنت
                Container(
                  color: Colors.black,
                  child: Column(
                    children: [
                      // صورة السؤال
                      Expanded(
                        flex: 3,
                        child: _buildQuestionImage(question.imageUrl ?? '',
                            quizProvider.currentSeries?.id ?? 1),
                      ),
                    ],
                  ),
                ),

                // تم إزالة شريط "أسئلة الإمتحان الرسمي"
              ],
            ),
          ),
        ),

        // مساحة إضافية بين الصورة وأزرار الإجابة (تم تقليلها)
        Container(
          width: double.infinity,
          height: 5,
          color: Colors.black,
        ),

        // أزرار الإجابة والتنقل
        Container(
          height: 300, // زيادة ارتفاع الأزرار أكثر
          color: Colors.black,
          child: Row(
            children: [
              // زر التالي (الأخضر على اليسار)
              Expanded(
                flex: 1,
                child: Container(
                  decoration: BoxDecoration(
                    // استخدام تدرج لوني للزر الأخضر
                    gradient: const LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Color(0xFF4CAF50), // أخضر فاتح
                        Color(0xFF2E7D32), // أخضر غامق
                      ],
                    ),
                    border: Border.all(color: Colors.black, width: 1),
                  ),
                  child: InkWell(
                    onTap: quizProvider.selectedAnswerIndices.isEmpty
                        ? null
                        : () {
                            quizProvider.submitAnswer();

                            if (quizProvider.status == QuizStatus.completed) {
                              Navigator.pushReplacement(
                                context,
                                MaterialPageRoute(
                                    builder: (context) => const ReviewScreen()),
                              );
                            }
                          },
                    child: const Center(
                      child: Icon(
                        Icons.check,
                        color: Colors.white,
                        size: 48, // تصغير حجم الأيقونة
                      ),
                    ),
                  ),
                ),
              ),

              // أزرار الإجابات (2×2)
              Expanded(
                flex: 4,
                child: Column(
                  children: [
                    // الصف الأول (1 و 2)
                    Expanded(
                      child: Row(
                        children: [
                          _buildAnswerButton(
                              context, 1, Colors.blue, quizProvider),
                          _buildAnswerButton(
                              context, 2, Colors.blue, quizProvider),
                        ],
                      ),
                    ),
                    // الصف الثاني (3 و 4)
                    Expanded(
                      child: Row(
                        children: [
                          _buildAnswerButton(
                              context, 3, Colors.blue, quizProvider),
                          _buildAnswerButton(
                              context, 4, Colors.blue, quizProvider),
                        ],
                      ),
                    ),
                  ],
                ),
              ),

              // زر مسح الاختيارات (الأحمر على اليمين)
              Expanded(
                flex: 1,
                child: Container(
                  decoration: BoxDecoration(
                    // استخدام تدرج لوني للزر الأحمر
                    gradient: const LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Color(0xFFF44336), // أحمر فاتح
                        Color(0xFFB71C1C), // أحمر غامق
                      ],
                    ),
                    border: Border.all(color: Colors.black, width: 1),
                  ),
                  child: InkWell(
                    onTap: () {
                      quizProvider.clearSelectedAnswers();
                    },
                    child: const Center(
                      child: Icon(
                        Icons.close,
                        color: Colors.white,
                        size: 48, // تصغير حجم الأيقونة
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildAnswerButton(
      BuildContext context, int index, Color color, QuizProvider quizProvider) {
    final isSelected = quizProvider.selectedAnswerIndices.contains(index - 1);

    return Expanded(
      child: InkWell(
        onTap: () {
          quizProvider.selectAnswer(index - 1);
        },
        child: Container(
          margin: const EdgeInsets.all(2),
          decoration: BoxDecoration(
            // استخدام تدرج لوني للأزرار
            gradient: isSelected
                ? const LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [Colors.amber, Color(0xFFFF9800)],
                  )
                : const LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Color(0xFF1565C0),
                      Color(0xFF0D47A1)
                    ], // تدرج أزرق غامق
                  ),
            border: Border.all(color: Colors.black, width: 1),
          ),
          child: Center(
            child: Text(
              '$index',
              style: const TextStyle(
                fontFamily: 'Cairo',
                fontSize: 48, // زيادة حجم الأرقام أكثر
                fontWeight: FontWeight.bold,
                color: Colors.white,
                shadows: [
                  Shadow(
                    offset: Offset(1.0, 1.0),
                    blurRadius: 3.0,
                    color: Color.fromARGB(150, 0, 0, 0),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  // متغير للتحكم في مرجع InteractiveViewer
  final Map<int, TransformationController> _transformationControllers = {};
  int _lastQuestionIndex = -1; // تتبع آخر مؤشر سؤال لمعرفة متى يتغير السؤال

  // دالة لعرض صورة السؤال (صور ثابتة لكل سؤال) مع إمكانية التكبير فقط
  Widget _buildQuestionImage(String imageUrl, int seriesId) {
    // طباعة رابط الصورة للتحقق منه
    debugPrint('رابط الصورة: $imageUrl، السلسلة: $seriesId');

    // إنشاء أو الحصول على وحدة تحكم للسؤال الحالي
    final quizProvider = Provider.of<QuizProvider>(
        WidgetsBinding.instance.focusManager.primaryFocus?.context ?? context,
        listen: false);
    final questionIndex = quizProvider.currentQuestionIndex;

    // إنشاء وحدة تحكم جديدة إذا لم تكن موجودة
    _transformationControllers[questionIndex] ??= TransformationController();

    // إعادة تعيين التكبير عند الانتقال إلى سؤال جديد
    if (_lastQuestionIndex != questionIndex) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _resetZoom(questionIndex);
      });
      _lastQuestionIndex = questionIndex;
    }

    // إضافة InteractiveViewer للسماح بالتكبير فقط
    return InteractiveViewer(
      transformationController: _transformationControllers[questionIndex],
      minScale: 1.0, // الحد الأدنى للتكبير هو الحجم الطبيعي
      maxScale: 4.0, // الحد الأقصى للتكبير
      boundaryMargin: const EdgeInsets.all(20), // هامش الحدود
      child: _buildImageWidget(imageUrl),
    );
  }

  // دالة لإعادة تعيين التكبير
  void _resetZoom(int questionIndex) {
    if (_transformationControllers.containsKey(questionIndex)) {
      _transformationControllers[questionIndex]!.value = Matrix4.identity();
    }
  }

  // تصميم الوضع الأفقي (الجديد)
  Widget _buildLandscapeLayout(
      BuildContext context, QuizProvider quizProvider, dynamic question) {
    // التحقق من أن السؤال ليس null وله خصائص صالحة
    if (question == null) {
      return const Center(
        child: CircularProgressIndicator(color: Color(0xFF0D47A1)),
      );
    }

    // تحويل الإجابات المحددة إلى نص للعرض (مثال: 0 2 0 4)
    String getSelectedAnswersText() {
      List<String> answers = ['0', '0', '0', '0'];
      for (int index in quizProvider.selectedAnswerIndices) {
        answers[index] = '${index + 1}';
      }
      return answers.join(' ');
    }

    return Row(
      children: [
        // الجانب الأيسر - أزرار الإجابة
        Container(
          width: 180, // زيادة العرض من 150 إلى 180
          color: Colors.black,
          child: Column(
            children: [
              // عرض الإجابات المحددة
              Container(
                height: 50,
                margin: const EdgeInsets.all(4),
                decoration: BoxDecoration(
                  color: Colors.black,
                  border: Border.all(color: Colors.white, width: 1),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Center(
                  child: Text(
                    getSelectedAnswersText(),
                    style: const TextStyle(
                      fontFamily: 'Cairo',
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),

              // زر المسح (الأحمر)
              Expanded(
                child: Container(
                  margin: const EdgeInsets.all(4),
                  decoration: BoxDecoration(
                    // استخدام تدرج لوني للزر الأحمر
                    gradient: const LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Color(0xFFF44336), // أحمر فاتح
                        Color(0xFFB71C1C), // أحمر غامق
                      ],
                    ),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: InkWell(
                    onTap: () {
                      quizProvider.clearSelectedAnswers();
                    },
                    child: const Center(
                      child: Icon(
                        Icons.close,
                        color: Colors.white,
                        size: 48, // تصغير حجم الأيقونة
                      ),
                    ),
                  ),
                ),
              ),

              // أزرار الإجابات (1-4)
              for (int i = 1; i <= 4; i++)
                Expanded(
                  child: Container(
                    margin: const EdgeInsets.all(4),
                    decoration: BoxDecoration(
                      // استخدام تدرج لوني للأزرار
                      gradient:
                          quizProvider.selectedAnswerIndices.contains(i - 1)
                              ? const LinearGradient(
                                  begin: Alignment.topCenter,
                                  end: Alignment.bottomCenter,
                                  colors: [Colors.amber, Color(0xFFFF9800)],
                                )
                              : const LinearGradient(
                                  begin: Alignment.topCenter,
                                  end: Alignment.bottomCenter,
                                  colors: [
                                    Color(0xFF1565C0),
                                    Color(0xFF0D47A1)
                                  ], // تدرج أزرق غامق
                                ),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: InkWell(
                      onTap: () {
                        quizProvider.selectAnswer(i - 1);
                      },
                      child: Center(
                        child: Text(
                          '$i',
                          style: const TextStyle(
                            fontFamily: 'Cairo',
                            fontSize: 36,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),

              // زر التأكيد (الأخضر)
              Expanded(
                child: Container(
                  margin: const EdgeInsets.all(4),
                  decoration: BoxDecoration(
                    // استخدام تدرج لوني للزر الأخضر
                    gradient: const LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Color(0xFF4CAF50), // أخضر فاتح
                        Color(0xFF2E7D32), // أخضر غامق
                      ],
                    ),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: InkWell(
                    onTap: quizProvider.selectedAnswerIndices.isEmpty
                        ? null
                        : () {
                            quizProvider.submitAnswer();
                            if (quizProvider.status == QuizStatus.completed) {
                              Navigator.pushReplacement(
                                context,
                                MaterialPageRoute(
                                    builder: (context) => const ReviewScreen()),
                              );
                            }
                          },
                    child: const Center(
                      child: Icon(
                        Icons.check,
                        color: Colors.white,
                        size: 48, // تصغير حجم الأيقونة
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),

        // الجزء الأوسط - صورة السؤال ونص السؤال
        Expanded(
          flex: 3,
          child: Container(
            color: Colors.black,
            child: Column(
              children: [
                // عرض المؤقت ورقم السؤال ورقم السلسلة
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      // رقم السؤال
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 4),
                        decoration: BoxDecoration(
                          color: Colors.grey[800],
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: Text(
                          '${quizProvider.currentQuestionIndex + 1}/${quizProvider.totalQuestions}',
                          style: const TextStyle(
                            fontFamily: 'Cairo',
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      // رقم السلسلة
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 4),
                        decoration: BoxDecoration(
                          color: Colors.grey[800],
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: Text(
                          'السلسلة ${quizProvider.currentSeries?.id ?? 1}',
                          style: const TextStyle(
                            fontFamily: 'Cairo',
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      // المؤقت
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 4),
                        decoration: BoxDecoration(
                          color: Colors.grey[800],
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: Row(
                          children: [
                            Icon(
                                quizProvider.isTimerPaused
                                    ? Icons.pause
                                    : Icons.timer,
                                color: Colors.white,
                                size: 16),
                            const SizedBox(width: 4),
                            Text(
                              '${quizProvider.remainingTime}s',
                              style: TextStyle(
                                fontFamily: 'Cairo',
                                color: quizProvider.isTimerPaused
                                    ? Colors.yellow
                                    : (quizProvider.remainingTime < 5
                                        ? Colors.red
                                        : Colors.white),
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                // صورة السؤال
                Expanded(
                  child: Stack(
                    children: [
                      _buildQuestionImage(question.imageUrl ?? '',
                          quizProvider.currentSeries?.id ?? 1),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),

        // الجانب الأيمن - زر العودة للصفحة الرئيسية
        Container(
          width: 60,
          color: const Color(0xFF0D47A1), // لون أزرق غامق مطابق للسلاسل
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // شعار التطبيق
              Padding(
                padding: const EdgeInsets.only(top: 8.0),
                child: Image.asset(
                  'assets/images/logo.png',
                  width: 40,
                  height: 40,
                  fit: BoxFit.contain,
                ),
              ),
              // زر العودة للصفحة الرئيسية
              IconButton(
                icon: const Icon(Icons.home, color: Colors.white, size: 40),
                onPressed: () {
                  showDialog(
                    context: context,
                    builder: (context) => AlertDialog(
                      title: const Text('إلغاء الاختبار',
                          style: TextStyle(fontFamily: 'Cairo')),
                      content: const Text('هل أنت متأكد من إلغاء الاختبار؟',
                          style: TextStyle(fontFamily: 'Cairo')),
                      actions: [
                        TextButton(
                          onPressed: () => Navigator.pop(context),
                          child: const Text('لا',
                              style: TextStyle(fontFamily: 'Cairo')),
                        ),
                        TextButton(
                          onPressed: () {
                            final quizProvider = Provider.of<QuizProvider>(
                                context,
                                listen: false);
                            quizProvider.resetQuiz();
                            Navigator.pop(context); // إغلاق الحوار
                            Navigator.pop(context); // العودة إلى شاشة السلاسل
                          },
                          child: const Text('نعم',
                              style: TextStyle(fontFamily: 'Cairo')),
                        ),
                      ],
                    ),
                  );
                },
              ),

              // زر إيقاف/تشغيل المؤقت
              IconButton(
                icon: Icon(
                  quizProvider.isTimerPaused ? Icons.play_arrow : Icons.pause,
                  color: Colors.white,
                  size: 40,
                ),
                onPressed: () {
                  // إيقاف/تشغيل المؤقت
                  quizProvider.toggleTimer();
                },
              ),

              // زر إعادة تشغيل الصوت
              IconButton(
                icon:
                    const Icon(Icons.volume_up, color: Colors.white, size: 40),
                onPressed: () {
                  quizProvider.replayAudio();
                },
              ),

              // زر إعادة تشغيل المؤقت
              IconButton(
                icon: const Icon(Icons.restart_alt,
                    color: Colors.white, size: 40),
                onPressed: () {
                  showDialog(
                    context: context,
                    builder: (context) => AlertDialog(
                      title: const Text('إعادة بدء السلسلة',
                          style: TextStyle(fontFamily: 'Cairo')),
                      content: const Text(
                          'هل أنت متأكد من إعادة بدء السلسلة من السؤال الأول؟',
                          style: TextStyle(fontFamily: 'Cairo')),
                      actions: [
                        TextButton(
                          onPressed: () => Navigator.pop(context),
                          child: const Text('لا',
                              style: TextStyle(fontFamily: 'Cairo')),
                        ),
                        TextButton(
                          onPressed: () {
                            final quizProvider = Provider.of<QuizProvider>(
                                context,
                                listen: false);
                            quizProvider.restartSeries();
                            Navigator.pop(context); // إغلاق الحوار
                          },
                          child: const Text('نعم',
                              style: TextStyle(fontFamily: 'Cairo')),
                        ),
                      ],
                    ),
                  );
                },
              ),
            ],
          ),
        ),
      ],
    );
  }

  // دالة مساعدة لبناء ويدجت الصورة
  Widget _buildImageWidget(String imageUrl) {
    // المعالجة العادية للصور
    // التحقق مما إذا كان المسار محلي أو عبر الإنترنت
    if (imageUrl.startsWith('assets/')) {
      // استخدام صورة محلية
      return Image.asset(
        imageUrl,
        fit: BoxFit.contain,
        errorBuilder: (context, error, stackTrace) {
          debugPrint('خطأ في تحميل الصورة المحلية: $error');
          debugPrint('رابط الصورة: $imageUrl');

          // في حالة حدوث خطأ، استخدم صورة ثابتة بديلة
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.broken_image, size: 64, color: Colors.grey[400]),
                const SizedBox(height: 16),
                const Text(
                  'تعذر تحميل الصورة',
                  style: TextStyle(
                    fontFamily: 'Cairo',
                    fontSize: 16,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          );
        },
      );
    } else {
      // استخدام صورة من الإنترنت
      return Image.network(
        imageUrl,
        fit: BoxFit.contain,
        loadingBuilder: (context, child, loadingProgress) {
          if (loadingProgress == null) {
            return child;
          }
          return Center(
            child: CircularProgressIndicator(
              value: loadingProgress.expectedTotalBytes != null
                  ? loadingProgress.cumulativeBytesLoaded /
                      loadingProgress.expectedTotalBytes!
                  : null,
              color: Colors.white,
            ),
          );
        },
        errorBuilder: (context, error, stackTrace) {
          debugPrint('خطأ في تحميل الصورة: $error');
          debugPrint('رابط الصورة: $imageUrl');

          // في حالة حدوث خطأ، استخدم صورة ثابتة
          return Image.network(
            'https://i.imgur.com/z8xHyT8.png',
            fit: BoxFit.contain,
            errorBuilder: (context, error, stackTrace) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.broken_image, size: 64, color: Colors.grey[400]),
                    const SizedBox(height: 16),
                    const Text(
                      'تعذر تحميل الصورة',
                      style: TextStyle(
                        fontFamily: 'Cairo',
                        fontSize: 16,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
              );
            },
          );
        },
      );
    }
  }
}
