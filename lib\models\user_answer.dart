class UserAnswer {
  final int questionId;
  final List<int> selectedAnswerIndices;
  final bool isCorrect;

  UserAnswer({
    required this.questionId,
    required this.selectedAnswerIndices,
    required this.isCorrect,
  });

  factory UserAnswer.fromJson(Map<String, dynamic> json) {
    return UserAnswer(
      questionId: json['questionId'],
      selectedAnswerIndices: List<int>.from(json['selectedAnswerIndices']),
      isCorrect: json['isCorrect'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'questionId': questionId,
      'selectedAnswerIndices': selectedAnswerIndices,
      'isCorrect': isCorrect,
    };
  }
}
