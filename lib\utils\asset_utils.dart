import 'dart:io';
import 'package:flutter/services.dart';
import 'package:flutter/material.dart';

class AssetUtils {
  // التحقق من وجود ملف في الأصول
  static Future<bool> assetExists(String path) async {
    try {
      await rootBundle.load(path);
      return true;
    } catch (e) {
      return false;
    }
  }

  // تحميل ملف من الأصول
  static Future<Uint8List?> loadAsset(String path) async {
    try {
      final ByteData data = await rootBundle.load(path);
      return data.buffer.asUint8List();
    } catch (e) {
      debugPrint('خطأ في تحميل الملف من الأصول: $e');
      return null;
    }
  }

  // نسخ ملف من الأصول إلى مسار محلي
  static Future<bool> copyAssetToFile(String assetPath, String filePath) async {
    try {
      final Uint8List? data = await loadAsset(assetPath);
      if (data == null) {
        return false;
      }

      final file = File(filePath);
      await file.writeAsBytes(data);
      return true;
    } catch (e) {
      debugPrint('خطأ في نسخ الملف من الأصول: $e');
      return false;
    }
  }
}
