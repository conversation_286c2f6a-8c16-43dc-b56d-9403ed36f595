$jsonFile = "assets/series/series_1/questions.json"
$jsonContent = Get-Content $jsonFile -Raw | ConvertFrom-Json

# Recorrer todas las preguntas y establecer el campo explanation como vacío
foreach ($question in $jsonContent.questions) {
    $question.explanation = ""
}

# Guardar el archivo JSON actualizado
$jsonFormatted = $jsonContent | ConvertTo-Json -Depth 10
$jsonFormatted | Set-Content $jsonFile

Write-Host "Se ha actualizado el archivo $jsonFile con los campos explanation vacíos."
