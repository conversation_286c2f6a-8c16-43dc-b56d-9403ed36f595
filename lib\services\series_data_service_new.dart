import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/series.dart';
import '../models/question.dart';

class SeriesDataServiceNew {
  static final SeriesDataServiceNew _instance =
      SeriesDataServiceNew._internal();
  factory SeriesDataServiceNew() => _instance;
  SeriesDataServiceNew._internal();

  // الحصول على قائمة السلاسل
  Future<List<Series>> getSeries() async {
    try {
      // قراءة ملف الفهرس
      final jsonString =
          await rootBundle.loadString('assets/series/index.json');
      final jsonData = jsonDecode(jsonString);
      final seriesList = jsonData['series'] as List<dynamic>;

      // تحويل البيانات إلى قائمة من السلاسل
      List<Series> series = [];
      for (var seriesData in seriesList) {
        final id = seriesData['id'] as int;
        final questions = await getSeriesQuestions(id);

        series.add(Series(
          id: id,
          title: seriesData['title'] as String,
          description: seriesData['description'] as String,
          questions: questions,
          isDownloaded: true, // افتراضياً، جميع السلاسل محملة
          isDownloading: false,
          totalQuestions: seriesData['questionsCount'] as int,
        ));
      }

      return series;
    } catch (e) {
      debugPrint('خطأ في الحصول على قائمة السلاسل: $e');
      return [];
    }
  }

  // الحصول على أسئلة سلسلة معينة
  Future<List<Question>> getSeriesQuestions(int seriesId) async {
    try {
      // قراءة ملف أسئلة السلسلة
      final questionsString = await rootBundle
          .loadString('assets/series/series_$seriesId/questions.json');
      final questionsData = jsonDecode(questionsString);
      final questionsList = questionsData['questions'] as List<dynamic>;

      // تحويل البيانات إلى قائمة من الأسئلة
      List<Question> questions = [];

      // المعالجة العادية لجميع السلاسل
      for (var questionData in questionsList) {
        try {
          // التحقق من وجود مفتاح 'questionText' أو 'text'
          String questionText = '';
          if (questionData.containsKey('questionText')) {
            questionText = questionData['questionText'] as String;
          } else if (questionData.containsKey('text')) {
            questionText = questionData['text'] as String;
          }

          // التحقق من وجود مفتاح 'answers' أو 'options'
          List<Answer> answers = [];
          if (questionData.containsKey('answers')) {
            answers = (questionData['answers'] as List<dynamic>)
                .map((answer) => Answer(
                    id: answer['id'] as int, text: answer['text'] as String))
                .toList();
          } else if (questionData.containsKey('options')) {
            answers = (questionData['options'] as List<dynamic>)
                .asMap()
                .entries
                .map((entry) =>
                    Answer(id: entry.key + 1, text: entry.value as String))
                .toList();
          }

          questions.add(Question(
            id: questionData['id'] as int,
            imageUrl: questionData['imageUrl'] as String,
            audioUrl: questionData['audioUrl'] as String,
            questionText: questionText,
            answers: answers,
            correctAnswerIndices:
                List<int>.from(questionData['correctAnswerIndices']),
            explanation: questionData['explanation'] as String,
          ));
        } catch (e) {
          debugPrint('خطأ في معالجة السؤال: $e');
          debugPrint('بيانات السؤال: $questionData');
        }
      }

      return questions;
    } catch (e) {
      debugPrint('خطأ في الحصول على أسئلة السلسلة $seriesId: $e');
      return [];
    }
  }

  // تحميل سلسلة إلى التخزين المحلي
  Future<bool> downloadSeries(int seriesId) async {
    try {
      debugPrint('بدء تحميل السلسلة $seriesId');

      // الحصول على مسار التخزين المحلي
      final appDir = await getApplicationDocumentsDirectory();
      final seriesDir = '${appDir.path}/series_$seriesId';

      // إنشاء المجلدات
      final seriesDirObj = Directory(seriesDir);
      if (!await seriesDirObj.exists()) {
        await seriesDirObj.create(recursive: true);
      }

      // إنشاء مجلد للملفات الصوتية
      final audioDir = Directory('$seriesDir/audio');
      if (!await audioDir.exists()) {
        await audioDir.create(recursive: true);
      }

      // قراءة ملف بيانات السلسلة
      final seriesDataString = await rootBundle
          .loadString('assets/series/series_$seriesId/data.json');
      final questionsString = await rootBundle
          .loadString('assets/series/series_$seriesId/questions.json');

      // نسخ ملفات البيانات
      final seriesDataFile = File('$seriesDir/data.json');
      await seriesDataFile.writeAsString(seriesDataString);

      final questionsFile = File('$seriesDir/questions.json');
      await questionsFile.writeAsString(questionsString);

      // نسخ الملفات الصوتية
      final questionsData = jsonDecode(questionsString);
      final questionsList = questionsData['questions'] as List<dynamic>;

      // نسخ الملفات الصوتية لجميع السلاسل
      for (var questionData in questionsList) {
        final audioUrl = questionData['audioUrl'] as String;
        final audioFileName = audioUrl.split('/').last;

        try {
          // نسخ الملف الصوتي من الأصول
          final audioData = await rootBundle
              .load('assets/series/series_$seriesId/audio/$audioFileName');
          final audioFile = File('$seriesDir/audio/$audioFileName');
          await audioFile.writeAsBytes(audioData.buffer.asUint8List());
        } catch (e) {
          debugPrint('خطأ في نسخ الملف الصوتي $audioFileName: $e');
          // إنشاء ملف صوتي افتراضي
          final audioFile = File('$seriesDir/audio/$audioFileName');
          await audioFile.writeAsString('محتوى افتراضي للملف الصوتي');
        }
      }

      debugPrint('تم تحميل السلسلة $seriesId بنجاح');
      return true;
    } catch (e) {
      debugPrint('خطأ في تحميل السلسلة $seriesId: $e');
      return false;
    }
  }

  // التحقق مما إذا كانت السلسلة محملة
  Future<bool> isSeriesDownloaded(int seriesId) async {
    try {
      final appDir = await getApplicationDocumentsDirectory();
      final seriesDir = '${appDir.path}/series_$seriesId';

      // التحقق من وجود المجلد
      final seriesDirObj = Directory(seriesDir);
      if (!await seriesDirObj.exists()) {
        return false;
      }

      // التحقق من وجود ملفات البيانات
      final seriesDataFile = File('$seriesDir/data.json');
      if (!await seriesDataFile.exists()) {
        return false;
      }

      final questionsFile = File('$seriesDir/questions.json');
      if (!await questionsFile.exists()) {
        return false;
      }

      // التحقق من وجود مجلد الملفات الصوتية
      final audioDir = Directory('$seriesDir/audio');
      if (!await audioDir.exists()) {
        return false;
      }

      return true;
    } catch (e) {
      debugPrint('خطأ في التحقق من تحميل السلسلة $seriesId: $e');
      return false;
    }
  }

  // حذف ملفات السلسلة
  Future<bool> deleteSeries(int seriesId) async {
    try {
      final appDir = await getApplicationDocumentsDirectory();
      final seriesDir = '${appDir.path}/series_$seriesId';

      final seriesDirObj = Directory(seriesDir);
      if (await seriesDirObj.exists()) {
        await seriesDirObj.delete(recursive: true);
        debugPrint('تم حذف مجلد السلسلة: $seriesDir');
        return true;
      }

      return false;
    } catch (e) {
      debugPrint('خطأ في حذف ملفات السلسلة $seriesId: $e');
      return false;
    }
  }

  // الحصول على مسار الملف الصوتي المحلي
  Future<String?> getLocalAudioPath(int seriesId, int questionId) async {
    try {
      final appDir = await getApplicationDocumentsDirectory();
      final audioPath =
          '${appDir.path}/series_$seriesId/audio/audio_$questionId.mp3';

      final file = File(audioPath);
      if (await file.exists()) {
        return audioPath;
      }

      return null;
    } catch (e) {
      debugPrint('خطأ في الحصول على مسار الملف الصوتي المحلي: $e');
      return null;
    }
  }

  // التحقق مما إذا كانت السلسلة مكتملة (تحتوي على جميع الصور والملفات الصوتية)
  Future<bool> isSeriesComplete(int seriesId) async {
    try {
      // التحقق من وجود السلسلة أولاً
      if (!await isSeriesDownloaded(seriesId)) {
        return false;
      }

      // جميع السلاسل المحملة تعتبر مكتملة
      return true;
    } catch (e) {
      debugPrint('خطأ في التحقق من اكتمال السلسلة $seriesId: $e');
      return false;
    }
  }

  // تحديث حالة اكتمال السلسلة
  Future<void> updateSeriesCompletionStatus(int seriesId) async {
    try {
      // التحقق من اكتمال السلسلة
      final isComplete = await isSeriesComplete(seriesId);

      // تحديث حالة السلسلة في SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('series_${seriesId}_complete', isComplete);

      debugPrint('تم تحديث حالة اكتمال السلسلة $seriesId: $isComplete');
    } catch (e) {
      debugPrint('خطأ في تحديث حالة اكتمال السلسلة $seriesId: $e');
    }
  }
}
