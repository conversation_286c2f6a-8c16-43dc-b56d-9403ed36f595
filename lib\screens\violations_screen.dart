import 'package:flutter/material.dart';

class ViolationsScreen extends StatefulWidget {
  const ViolationsScreen({Key? key}) : super(key: key);

  @override
  State<ViolationsScreen> createState() => _ViolationsScreenState();
}

class _ViolationsScreenState extends State<ViolationsScreen> {
  // قائمة بمعلومات المخالفات
  final List<Map<String, dynamic>> _violations = [
    {
      'image': 'assets/images/violations/violation1.png',
      'description': 'مخالفات السرعة',
    },
    {
      'image': 'assets/images/violations/violation2.png',
      'description': 'مخالفات الوقوف',
    },
    {
      'image': 'assets/images/violations/violation3.png',
      'description': 'مخالفات الإشارات',
    },
    {
      'image': 'assets/images/violations/violation4.png',
      'description': 'مخالفات الوثائق',
    },
    {
      'image': 'assets/images/violations/violation5.png',
      'description': 'مخالفات حزام الأمان',
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true, // تمديد الخلفية خلف شريط التطبيق
      appBar: AppBar(
        title: const Text(
          'المخالفات',
          style: TextStyle(
            fontFamily: 'Cairo',
            fontWeight: FontWeight.bold,
            color: Colors.white,
            shadows: [
              Shadow(
                offset: Offset(1.0, 1.0),
                blurRadius: 3.0,
                color: Color.fromARGB(150, 0, 0, 0),
              ),
            ],
          ),
        ),
        backgroundColor: Colors.transparent, // جعل الشريط شفافًا
        elevation: 0, // إزالة الظل
        centerTitle: true,
        // إضافة تأثير زجاجي للشريط
        flexibleSpace: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Color.fromRGBO(0, 0, 0, 0.4),
                Color.fromRGBO(0, 0, 0, 0.1),
              ],
            ),
          ),
        ),
        // إزالة leading وإضافة زر العودة في actions على اليمين
        automaticallyImplyLeading: false, // إلغاء زر العودة التلقائي
        actions: [
          IconButton(
            icon: const Icon(
              Icons.arrow_forward,
              color: Colors.white,
              shadows: [
                Shadow(
                  offset: Offset(1.0, 1.0),
                  blurRadius: 3.0,
                  color: Color.fromARGB(150, 0, 0, 0),
                ),
              ],
            ), // سهم للأمام (يمين) للعودة
            onPressed: () {
              Navigator.pop(context);
            },
          ),
        ],
      ),
      body: Container(
        decoration: BoxDecoration(
          // استخدام صورة خلفية مختلفة حسب اتجاه الشاشة
          image: DecorationImage(
            image: AssetImage(
              MediaQuery.of(context).orientation == Orientation.portrait
                  ? 'assets/images/backgrounds/portrait_bg.jpg'
                  : 'assets/images/backgrounds/landscape_bg.jpg',
            ),
            fit: BoxFit.cover,
          ),
        ),
        child: Container(
          // طبقة شفافة فوق الخلفية لتحسين وضوح النص والأزرار
          decoration: const BoxDecoration(
            color: Color.fromRGBO(0, 0, 0, 0.4), // طبقة سوداء شفافة
          ),
          child: Padding(
            // إضافة مسافة إضافية في الأعلى لتعويض الشريط الشفاف
            padding: const EdgeInsets.only(top: 80.0),
            child: OrientationBuilder(
              builder: (context, orientation) {
                // التحقق من اتجاه الشاشة لتحديد عدد الأعمدة
                final isLandscape = orientation == Orientation.landscape;

                return GridView.builder(
                  padding: const EdgeInsets.all(16),
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: isLandscape
                        ? 2
                        : 1, // عمودان في الوضع الأفقي، عمود واحد في الوضع الرأسي
                    childAspectRatio:
                        1, // نسبة العرض إلى الارتفاع 1:1 للحفاظ على الشكل المربع
                    crossAxisSpacing: 16,
                    mainAxisSpacing: 16,
                  ),
                  itemCount: _violations.length,
                  itemBuilder: (context, index) {
                    return _buildViolationCard(
                      imagePath: _violations[index]['image'],
                      description: _violations[index]['description'],
                    );
                  },
                );
              },
            ),
          ),
        ),
      ),
    );
  }

  // دالة لبناء بطاقة المخالفة
  Widget _buildViolationCard({
    required String imagePath,
    required String description,
  }) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // حساب عرض البطاقة بناءً على عرض الشاشة
        // استخدام نسبة من عرض الشاشة مع هوامش
        final screenWidth = MediaQuery.of(context).size.width;
        final cardSize = screenWidth > 600
            ? screenWidth * 0.4 // للشاشات الكبيرة
            : screenWidth * 0.8; // للشاشات الصغيرة

        // التأكد من أن البطاقة مربعة
        final cardWidth = cardSize;
        final cardHeight = cardSize;

        return Center(
          child: Card(
            margin: const EdgeInsets.only(bottom: 16),
            elevation: 4,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            child: AspectRatio(
              aspectRatio:
                  1, // نسبة العرض إلى الارتفاع 1:1 للحفاظ على الشكل المربع
              child: ClipRRect(
                borderRadius: BorderRadius.circular(12),
                child: Image.asset(
                  imagePath,
                  width: cardWidth,
                  height: cardHeight,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    // في حالة حدوث خطأ في تحميل الصورة
                    return Container(
                      width: cardWidth,
                      height: cardHeight,
                      color: Colors.grey.shade200,
                      child: const Center(
                        child: Icon(
                          Icons.image_not_supported,
                          size: 50,
                          color: Colors.grey,
                        ),
                      ),
                    );
                  },
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
