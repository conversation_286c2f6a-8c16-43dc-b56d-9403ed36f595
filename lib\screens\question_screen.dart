import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/quiz_provider.dart';
import 'review_screen.dart';

class QuestionScreen extends StatelessWidget {
  const QuestionScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Consumer<QuizProvider>(
          builder: (context, quizProvider, child) {
            return Text(
              'السؤال ${quizProvider.currentQuestionIndex + 1} من ${quizProvider.totalQuestions}',
              style: const TextStyle(
                fontFamily: 'Cairo',
                fontWeight: FontWeight.bold,
              ),
            );
          },
        ),
        leading: IconButton(
          icon: const Icon(Icons.close),
          onPressed: () {
            showDialog(
              context: context,
              builder: (context) => AlertDialog(
                title: const Text('إلغاء الاختبار', style: TextStyle(fontFamily: 'Cairo')),
                content: const Text('هل أنت متأكد من إلغاء الاختبار؟', style: TextStyle(fontFamily: 'Cairo')),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.pop(context),
                    child: const Text('لا', style: TextStyle(fontFamily: 'Cairo')),
                  ),
                  TextButton(
                    onPressed: () {
                      final quizProvider = Provider.of<QuizProvider>(context, listen: false);
                      quizProvider.resetQuiz();
                      Navigator.pop(context); // إغلاق الحوار
                      Navigator.pop(context); // العودة إلى شاشة السلاسل
                    },
                    child: const Text('نعم', style: TextStyle(fontFamily: 'Cairo')),
                  ),
                ],
              ),
            );
          },
        ),
      ),
      body: Consumer<QuizProvider>(
        builder: (context, quizProvider, child) {
          final question = quizProvider.currentQuestion;

          if (question == null) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          return Column(
            children: [
              // عداد الوقت
              LinearProgressIndicator(
                value: quizProvider.remainingTime / quizProvider.timerDuration,
                backgroundColor: Colors.grey[300],
                valueColor: AlwaysStoppedAnimation<Color>(
                  quizProvider.remainingTime < 5 ? Colors.red : Colors.blue,
                ),
              ),
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: Text(
                  'الوقت المتبقي: ${quizProvider.remainingTime} ثانية',
                  style: TextStyle(
                    fontFamily: 'Cairo',
                    fontWeight: FontWeight.bold,
                    color: quizProvider.remainingTime < 5 ? Colors.red : Colors.black,
                  ),
                ),
              ),

              // صورة السؤال
              Expanded(
                flex: 3,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Container(
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        // في التطبيق الحقيقي، استبدل هذا بصورة حقيقية
                        Icon(
                          Icons.image,
                          size: 100,
                          color: Colors.grey[400],
                        ),
                        const SizedBox(height: 16),
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 16.0),
                          child: Text(
                            question.questionText,
                            textAlign: TextAlign.center,
                            style: const TextStyle(
                              fontFamily: 'Cairo',
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),

              // زر تكرار الصوت
              ElevatedButton.icon(
                onPressed: () {
                  quizProvider.replayAudio();
                },
                icon: const Icon(Icons.volume_up),
                label: const Text(
                  'تكرار الصوت',
                  style: TextStyle(fontFamily: 'Cairo'),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                ),
              ),

              // الإجابات
              Expanded(
                flex: 4,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: ListView.builder(
                    itemCount: question.answers.length,
                    itemBuilder: (context, index) {
                      final answer = question.answers[index];
                      final isSelected = quizProvider.selectedAnswerIndices.contains(index);

                      return Padding(
                        padding: const EdgeInsets.only(bottom: 8.0),
                        child: InkWell(
                          onTap: () {
                            quizProvider.selectAnswer(index);
                          },
                          child: Container(
                            padding: const EdgeInsets.all(16.0),
                            decoration: BoxDecoration(
                              color: isSelected ? Colors.blue.shade100 : Colors.white,
                              border: Border.all(
                                color: isSelected ? Colors.blue : Colors.grey,
                                width: isSelected ? 2 : 1,
                              ),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Row(
                              children: [
                                Container(
                                  width: 30,
                                  height: 30,
                                  decoration: BoxDecoration(
                                    color: isSelected ? Colors.blue : Colors.grey[300],
                                    shape: BoxShape.circle,
                                  ),
                                  child: Center(
                                    child: Text(
                                      '${answer.id}',
                                      style: TextStyle(
                                        fontFamily: 'Cairo',
                                        color: isSelected ? Colors.white : Colors.black,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 16),
                                Expanded(
                                  child: Text(
                                    answer.text,
                                    style: const TextStyle(
                                      fontFamily: 'Cairo',
                                      fontSize: 16,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ),

              // أزرار التحكم
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    // زر مسح الاختيارات
                    ElevatedButton(
                      onPressed: quizProvider.selectedAnswerIndices.isEmpty
                          ? null
                          : () {
                              quizProvider.clearSelectedAnswers();
                            },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.red,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: const Text(
                        'مسح الاختيارات',
                        style: TextStyle(
                          fontFamily: 'Cairo',
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),

                    // زر التالي
                    ElevatedButton(
                      onPressed: quizProvider.selectedAnswerIndices.isEmpty
                          ? null
                          : () {
                              quizProvider.submitAnswer();

                              if (quizProvider.status == QuizStatus.completed) {
                                Navigator.pushReplacement(
                                  context,
                                  MaterialPageRoute(builder: (context) => const ReviewScreen()),
                                );
                              }
                            },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: const Text(
                        'التالي',
                        style: TextStyle(
                          fontFamily: 'Cairo',
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          );
        },
      ),
    );
  }
}
