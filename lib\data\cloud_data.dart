class CloudData {
  // روابط الصور السحابية
  static Map<int, List<String>> getCloudImageUrls() {
    return {
      1: [
        'https://i.imgur.com/z8xHyT8.jpg', // صورة السؤال الأول في السلسلة الأولى
      ],
      2: [
        'https://i.imgur.com/XqwRRVt.jpg', // صورة السؤال الأول في السلسلة الثانية
      ],
      3: [
        'https://i.imgur.com/z8xHyT8.jpg', // صورة السؤال الأول في السلسلة الثالثة
      ],
    };
  }

  // روابط الملفات الصوتية السحابية
  static Map<int, List<String>> getCloudAudioUrls() {
    return {
      1: [
        'assets/audio/series_1_audio_1.mp3', // ملف صوتي للسؤال الأول في السلسلة الأولى
      ],
      2: [
        'assets/audio/series_1_audio_1.mp3', // ملف صوتي للسؤال الأول في السلسلة الثانية
      ],
      3: [
        'assets/audio/series_1_audio_1.mp3', // ملف صوتي للسؤال الأول في السلسلة الثالثة
      ],
    };
  }

  // الإجابات الصحيحة
  static Map<int, Map<int, List<int>>> getCloudCorrectAnswers() {
    return {
      1: _getSeriesAnswers(1),
      2: _getSeriesAnswers(2),
      3: _getSeriesAnswers(3),
    };
  }

  // إنشاء إجابات افتراضية لسلسلة معينة
  static Map<int, List<int>> _getSeriesAnswers(int seriesId) {
    Map<int, List<int>> answers = {};

    for (int i = 1; i <= 40; i++) {
      answers[i] = [0]; // الإجابة الافتراضية هي الخيار الأول
    }

    return answers;
  }
}
