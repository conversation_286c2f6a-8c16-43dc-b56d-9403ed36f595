import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';
import '../models/series.dart';
import '../models/question.dart';

class SeriesDataService {
  static final SeriesDataService _instance = SeriesDataService._internal();
  factory SeriesDataService() => _instance;
  SeriesDataService._internal();

  // الحصول على قائمة السلاسل
  Future<List<Series>> getSeries() async {
    try {
      // قراءة ملف الفهرس
      final jsonString = await rootBundle.loadString('assets/series/index.json');
      final jsonData = jsonDecode(jsonString);
      final seriesList = jsonData['series'] as List<dynamic>;
      
      // تحويل البيانات إلى قائمة من السلاسل
      List<Series> series = [];
      for (var seriesData in seriesList) {
        final id = seriesData['id'] as int;
        final questions = await getSeriesQuestions(id);
        
        series.add(Series(
          id: id,
          title: seriesData['title'] as String,
          description: seriesData['description'] as String,
          questions: questions,
          isDownloaded: true, // افتراضياً، جميع السلاسل محملة
          isDownloading: false,
          totalQuestions: seriesData['questionsCount'] as int,
        ));
      }
      
      return series;
    } catch (e) {
      debugPrint('خطأ في الحصول على قائمة السلاسل: $e');
      return [];
    }
  }

  // الحصول على أسئلة سلسلة معينة
  Future<List<Question>> getSeriesQuestions(int seriesId) async {
    try {
      // قراءة ملف بيانات السلسلة
      final seriesDataString = await rootBundle.loadString('assets/series/series_$seriesId/data.json');
      final seriesData = jsonDecode(seriesDataString);
      final questionsCount = seriesData['questionsCount'] as int;
      
      // قراءة بيانات كل سؤال
      List<Question> questions = [];
      for (int i = 1; i <= questionsCount; i++) {
        try {
          final questionDataString = await rootBundle.loadString('assets/series/series_$seriesId/questions/$i/data.json');
          final questionData = jsonDecode(questionDataString);
          
          questions.add(Question(
            id: questionData['id'] as int,
            imageUrl: questionData['imageUrl'] as String,
            audioUrl: questionData['audioUrl'] as String,
            questionText: questionData['questionText'] as String,
            answers: (questionData['answers'] as List<dynamic>).map((answer) => 
              Answer(id: answer['id'] as int, text: answer['text'] as String)
            ).toList(),
            correctAnswerIndices: List<int>.from(questionData['correctAnswerIndices']),
            explanation: questionData['explanation'] as String,
          ));
        } catch (e) {
          debugPrint('خطأ في قراءة بيانات السؤال $i في السلسلة $seriesId: $e');
        }
      }
      
      return questions;
    } catch (e) {
      debugPrint('خطأ في الحصول على أسئلة السلسلة $seriesId: $e');
      return [];
    }
  }

  // تحميل سلسلة إلى التخزين المحلي
  Future<bool> downloadSeries(int seriesId) async {
    try {
      debugPrint('بدء تحميل السلسلة $seriesId');
      
      // الحصول على مسار التخزين المحلي
      final appDir = await getApplicationDocumentsDirectory();
      final seriesDir = '${appDir.path}/series_$seriesId';
      
      // إنشاء المجلدات
      final seriesDirObj = Directory(seriesDir);
      if (!await seriesDirObj.exists()) {
        await seriesDirObj.create(recursive: true);
      }
      
      // قراءة ملف بيانات السلسلة
      final seriesDataString = await rootBundle.loadString('assets/series/series_$seriesId/data.json');
      final seriesData = jsonDecode(seriesDataString);
      final questionsCount = seriesData['questionsCount'] as int;
      
      // نسخ ملف بيانات السلسلة
      final seriesDataFile = File('$seriesDir/data.json');
      await seriesDataFile.writeAsString(seriesDataString);
      
      // نسخ بيانات كل سؤال
      for (int i = 1; i <= questionsCount; i++) {
        try {
          // إنشاء مجلد السؤال
          final questionDir = '$seriesDir/questions/$i';
          final questionDirObj = Directory(questionDir);
          if (!await questionDirObj.exists()) {
            await questionDirObj.create(recursive: true);
          }
          
          // نسخ ملف بيانات السؤال
          final questionDataString = await rootBundle.loadString('assets/series/series_$seriesId/questions/$i/data.json');
          final questionDataFile = File('$questionDir/data.json');
          await questionDataFile.writeAsString(questionDataString);
          
          // نسخ ملف الصوت
          final audioData = await rootBundle.load('assets/series/series_$seriesId/questions/$i/audio.mp3');
          final audioFile = File('$questionDir/audio.mp3');
          await audioFile.writeAsBytes(audioData.buffer.asUint8List());
        } catch (e) {
          debugPrint('خطأ في نسخ بيانات السؤال $i في السلسلة $seriesId: $e');
        }
      }
      
      debugPrint('تم تحميل السلسلة $seriesId بنجاح');
      return true;
    } catch (e) {
      debugPrint('خطأ في تحميل السلسلة $seriesId: $e');
      return false;
    }
  }

  // التحقق مما إذا كانت السلسلة محملة
  Future<bool> isSeriesDownloaded(int seriesId) async {
    try {
      final appDir = await getApplicationDocumentsDirectory();
      final seriesDir = '${appDir.path}/series_$seriesId';
      
      // التحقق من وجود المجلد
      final seriesDirObj = Directory(seriesDir);
      if (!await seriesDirObj.exists()) {
        return false;
      }
      
      // التحقق من وجود ملف بيانات السلسلة
      final seriesDataFile = File('$seriesDir/data.json');
      if (!await seriesDataFile.exists()) {
        return false;
      }
      
      // قراءة ملف بيانات السلسلة
      final seriesDataString = await seriesDataFile.readAsString();
      final seriesData = jsonDecode(seriesDataString);
      final questionsCount = seriesData['questionsCount'] as int;
      
      // التحقق من وجود بيانات كل سؤال
      for (int i = 1; i <= questionsCount; i++) {
        final questionDir = '$seriesDir/questions/$i';
        final questionDirObj = Directory(questionDir);
        if (!await questionDirObj.exists()) {
          return false;
        }
        
        final questionDataFile = File('$questionDir/data.json');
        if (!await questionDataFile.exists()) {
          return false;
        }
        
        final audioFile = File('$questionDir/audio.mp3');
        if (!await audioFile.exists()) {
          return false;
        }
      }
      
      return true;
    } catch (e) {
      debugPrint('خطأ في التحقق من تحميل السلسلة $seriesId: $e');
      return false;
    }
  }

  // حذف ملفات السلسلة
  Future<bool> deleteSeries(int seriesId) async {
    try {
      final appDir = await getApplicationDocumentsDirectory();
      final seriesDir = '${appDir.path}/series_$seriesId';
      
      final seriesDirObj = Directory(seriesDir);
      if (await seriesDirObj.exists()) {
        await seriesDirObj.delete(recursive: true);
        debugPrint('تم حذف مجلد السلسلة: $seriesDir');
        return true;
      }
      
      return false;
    } catch (e) {
      debugPrint('خطأ في حذف ملفات السلسلة $seriesId: $e');
      return false;
    }
  }

  // الحصول على مسار الملف الصوتي المحلي
  Future<String?> getLocalAudioPath(int seriesId, int questionId) async {
    try {
      final appDir = await getApplicationDocumentsDirectory();
      final audioPath = '${appDir.path}/series_$seriesId/questions/$questionId/audio.mp3';
      
      final file = File(audioPath);
      if (await file.exists()) {
        return audioPath;
      }
      
      return null;
    } catch (e) {
      debugPrint('خطأ في الحصول على مسار الملف الصوتي المحلي: $e');
      return null;
    }
  }
}
