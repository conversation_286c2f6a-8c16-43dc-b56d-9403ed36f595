import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:photo_view/photo_view.dart';
import '../providers/lessons_quiz_provider.dart';
import '../models/question.dart';
import '../models/user_answer.dart';
import 'lessons_series_screen.dart';

class LessonsReviewScreen extends StatefulWidget {
  final int? seriesId;
  const LessonsReviewScreen({Key? key, this.seriesId}) : super(key: key);

  @override
  State<LessonsReviewScreen> createState() => _LessonsReviewScreenState();
}

class _LessonsReviewScreenState extends State<LessonsReviewScreen> {
  // دالة للتحقق من اتجاه الشاشة (أفقي أو عمودي)
  bool isLandscape(BuildContext context) {
    return MediaQuery.of(context).orientation == Orientation.landscape;
  }

  @override
  void initState() {
    super.initState();
    // استعادة إجابات المستخدم عند فتح الشاشة
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      if (widget.seriesId != null) {
        try {
          // تحميل بيانات السلسلة من LessonsQuizProvider
          final quizProvider = Provider.of<LessonsQuizProvider>(
            context,
            listen: false,
          );

          // طباعة معلومات تشخيصية
          debugPrint('تحميل بيانات السلسلة رقم: ${widget.seriesId}');
          debugPrint('حالة الاختبار: ${quizProvider.status}');
          debugPrint('عدد الإجابات: ${quizProvider.userAnswers.length}');

          // تحميل السلسلة بغض النظر عن حالة الإجابات
          debugPrint('محاولة تحميل السلسلة وإجاباتها');
          await _loadSeriesData(widget.seriesId!);

          // تحديث واجهة المستخدم
          if (mounted) {
            setState(() {
              debugPrint('تم تحديث واجهة المستخدم');
            });
          }
        } catch (e) {
          debugPrint('خطأ في تحميل بيانات السلسلة: $e');
        }
      }
    });
  }

  // دالة لتحميل بيانات السلسلة
  Future<void> _loadSeriesData(int seriesId) async {
    try {
      // الحصول على مزود الاختبار
      final quizProvider = Provider.of<LessonsQuizProvider>(
        context,
        listen: false,
      );

      // تحميل السلسلة مباشرة باستخدام الدالة الجديدة
      final series = await quizProvider.loadSeriesById(seriesId);

      if (series != null) {
        debugPrint('تم تحميل السلسلة رقم $seriesId بنجاح');
        debugPrint('عدد الأسئلة: ${series.questions.length}');
        debugPrint('عدد الإجابات المحملة: ${quizProvider.userAnswers.length}');

        // تحديث واجهة المستخدم
        if (mounted) {
          setState(() {});
        }
      } else {
        debugPrint('فشل في تحميل السلسلة رقم $seriesId');
      }
    } catch (e) {
      debugPrint('خطأ في تحميل بيانات السلسلة: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: isLandscape(context)
          ? null // إخفاء AppBar في الوضع الأفقي
          : AppBar(
              // استخدام title مع centerTitle لوضع الشعار في المنتصف
              title: Image.asset(
                'assets/images/logo.png',
                width: 40,
                height: 40,
                fit: BoxFit.contain,
              ),
              centerTitle: true,
              backgroundColor: const Color(
                0xFF0D47A1,
              ), // لون أزرق غامق مطابق للسلاسل
              automaticallyImplyLeading: false,
              leading: IconButton(
                icon: const Icon(Icons.home, color: Colors.white),
                onPressed: () {
                  Navigator.pushReplacement(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const LessonsSeriesScreen(),
                    ),
                  );
                },
              ),
            ),
      backgroundColor: const Color(0xFF1A1E2E), // لون خلفية داكن
      body: Consumer<LessonsQuizProvider>(
        builder: (context, quizProvider, child) {
          final series = quizProvider.currentSeries;
          final userAnswers = quizProvider.userAnswers;

          if (series == null) {
            return const Center(
              child: CircularProgressIndicator(color: Color(0xFF0D47A1)),
            );
          }

          final correctCount = quizProvider.correctAnswersCount;
          final totalQuestions = quizProvider.totalQuestions;

          // استخدام تصميم مختلف حسب اتجاه الشاشة
          if (isLandscape(context)) {
            return _buildLandscapeLayout(
              context,
              quizProvider,
              series,
              userAnswers,
              correctCount,
              totalQuestions,
            );
          } else {
            return _buildPortraitLayout(
              context,
              quizProvider,
              series,
              userAnswers,
              correctCount,
              totalQuestions,
            );
          }
        },
      ),
    );
  }

  // تصميم الوضع الأفقي (المعدل)
  Widget _buildLandscapeLayout(
    BuildContext context,
    LessonsQuizProvider quizProvider,
    dynamic series,
    List<UserAnswer> userAnswers,
    int correctCount,
    int totalQuestions,
  ) {
    return Column(
      children: [
        // الشريط العلوي - شريط التحكم
        Container(
          width: double.infinity,
          height: 60,
          color: const Color(0xFF0D47A1), // لون أزرق غامق مطابق للسلاسل
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // زر العودة للصفحة الرئيسية
              IconButton(
                icon: const Icon(Icons.home, color: Colors.white, size: 32),
                onPressed: () {
                  Navigator.pushReplacement(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const LessonsSeriesScreen(),
                    ),
                  );
                },
              ),

              // شعار التطبيق فقط في المنتصف
              Image.asset(
                'assets/images/logo.png',
                width: 40,
                height: 40,
                fit: BoxFit.contain,
              ),

              // زر مشاركة النتيجة
              IconButton(
                icon: const Icon(Icons.share, color: Colors.white, size: 32),
                onPressed: () {
                  _shareResult(
                    context,
                    correctCount,
                    totalQuestions,
                    series.title,
                  );
                },
              ),
            ],
          ),
        ),

        // عرض النتيجة
        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(vertical: 10),
          color: const Color(0xFF1A1E2E), // لون خلفية داكن
          child: Text(
            'النتيجة $correctCount/$totalQuestions',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontFamily: 'Cairo',
              fontSize: 28,
              fontWeight: FontWeight.bold,
              color: correctCount >= (totalQuestions / 2)
                  ? Colors.green
                  : Colors.red,
            ),
          ),
        ),

        // شبكة الأسئلة
        Expanded(
          child: Padding(
            padding: const EdgeInsets.fromLTRB(
              6.0,
              6.0,
              6.0,
              24.0,
            ), // زيادة الهامش السفلي في الوضع الأفقي
            child: LayoutBuilder(
              builder: (context, constraints) {
                // حساب الارتفاع المتاح للشبكة
                final availableHeight = constraints.maxHeight;
                // حساب الارتفاع المتاح لكل صف (4 صفوف)
                final rowHeight =
                    (availableHeight - 3 * 4) / 4; // 4 صفوف و 3 مسافات بينها
                // حساب نسبة العرض إلى الارتفاع
                final aspectRatio =
                    ((MediaQuery.of(context).size.width - 9 * 4) / 10) /
                        rowHeight;

                // إنشاء قائمة من الأسئلة مرتبة بحيث تبدأ من اليمين في كل صف
                final List<Widget> questionWidgets = [];

                // عدد الصفوف المطلوبة
                final int rowCount = (userAnswers.length / 10).ceil();

                // إنشاء الصفوف
                for (int row = 0; row < rowCount; row++) {
                  // إنشاء الأعمدة في كل صف (من اليمين إلى اليسار)
                  for (int col = 9; col >= 0; col--) {
                    // حساب الفهرس في القائمة الأصلية
                    final int originalIndex = row * 10 + col;

                    // التأكد من أن الفهرس ضمن النطاق
                    if (originalIndex < userAnswers.length) {
                      final userAnswer = userAnswers[originalIndex];
                      final question = _findQuestionById(
                        series.questions,
                        userAnswer.questionId,
                      );

                      if (question != null) {
                        questionWidgets.add(
                          _buildQuestionGridItem(
                            context,
                            question,
                            userAnswer,
                            originalIndex + 1,
                            originalIndex,
                            userAnswers,
                            series.questions,
                          ),
                        );
                      } else {
                        questionWidgets.add(const SizedBox.shrink());
                      }
                    } else {
                      // إضافة مساحة فارغة للمواقع غير المستخدمة
                      questionWidgets.add(const SizedBox.shrink());
                    }
                  }
                }

                return GridView.count(
                  padding: const EdgeInsets.only(
                    bottom: 20.0,
                  ), // زيادة الهامش السفلي للقائمة في الوضع الأفقي
                  crossAxisCount: 10, // عدد ثابت من الأعمدة
                  childAspectRatio:
                      aspectRatio, // نسبة ديناميكية بناءً على ارتفاع الشاشة
                  crossAxisSpacing: 4, // تقليل المسافة الأفقية بين المربعات
                  mainAxisSpacing: 4, // تقليل المسافة العمودية بين المربعات
                  children: questionWidgets,
                );
              },
            ),
          ),
        ),

        // مساحة إضافية في الأسفل
        const SizedBox(height: 10.0),
      ],
    );
  }

  // تصميم الوضع العمودي (الحالي)
  Widget _buildPortraitLayout(
    BuildContext context,
    LessonsQuizProvider quizProvider,
    dynamic series,
    List<UserAnswer> userAnswers,
    int correctCount,
    int totalQuestions,
  ) {
    return Column(
      children: [
        // عنوان النتيجة
        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(vertical: 20),
          child: Text(
            'النتيجة $correctCount/$totalQuestions',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontFamily: 'Cairo',
              fontSize: 36,
              fontWeight: FontWeight.bold,
              color: correctCount >= (totalQuestions / 2)
                  ? Colors.green
                  : Colors.red,
            ),
          ),
        ),

        // شبكة الأسئلة
        Expanded(
          child: Padding(
            padding: const EdgeInsets.fromLTRB(
              8.0,
              8.0,
              8.0,
              16.0,
            ), // إضافة هامش أكبر في الأسفل
            child: Builder(
              builder: (context) {
                // إنشاء قائمة من الأسئلة مرتبة بحيث تبدأ من اليمين في كل صف
                final List<Widget> questionWidgets = [];

                // عدد الصفوف المطلوبة
                final int rowCount = (userAnswers.length / 5).ceil();

                // إنشاء الصفوف
                for (int row = 0; row < rowCount; row++) {
                  // إنشاء الأعمدة في كل صف (من اليمين إلى اليسار)
                  for (int col = 4; col >= 0; col--) {
                    // حساب الفهرس في القائمة الأصلية
                    final int originalIndex = row * 5 + col;

                    // التأكد من أن الفهرس ضمن النطاق
                    if (originalIndex < userAnswers.length) {
                      final userAnswer = userAnswers[originalIndex];
                      final question = _findQuestionById(
                        series.questions,
                        userAnswer.questionId,
                      );

                      if (question != null) {
                        questionWidgets.add(
                          _buildQuestionGridItem(
                            context,
                            question,
                            userAnswer,
                            originalIndex + 1,
                            originalIndex,
                            userAnswers,
                            series.questions,
                          ),
                        );
                      } else {
                        questionWidgets.add(const SizedBox.shrink());
                      }
                    } else {
                      // إضافة مساحة فارغة للمواقع غير المستخدمة
                      questionWidgets.add(const SizedBox.shrink());
                    }
                  }
                }

                return GridView.count(
                  padding: const EdgeInsets.only(
                    bottom: 10.0,
                  ), // إضافة هامش سفلي للقائمة
                  crossAxisCount: 5, // 5 أسئلة في كل صف
                  childAspectRatio: 1.0, // مربعات متساوية
                  crossAxisSpacing: 10, // المسافة الأفقية بين المربعات
                  mainAxisSpacing: 10, // المسافة العمودية بين المربعات
                  children: questionWidgets,
                );
              },
            ),
          ),
        ),

        // أزرار التحكم
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              // زر مشاركة النتيجة
              ElevatedButton.icon(
                onPressed: () {
                  _shareResult(
                    context,
                    correctCount,
                    totalQuestions,
                    series.title,
                  );
                },
                icon: const Icon(Icons.share, color: Colors.white),
                label: const Text(
                  'مشاركة النتيجة',
                  style: TextStyle(
                    fontFamily: 'Cairo',
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                    color: Colors.white,
                  ),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 20,
                    vertical: 12,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),

              // زر العودة
              ElevatedButton.icon(
                onPressed: () {
                  // إعادة تعيين الاختبار بشكل متزامن (دون انتظار)
                  quizProvider.resetQuiz();

                  // العودة للرئيسية
                  Navigator.pushReplacement(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const LessonsSeriesScreen(),
                    ),
                  );
                },
                icon: const Icon(Icons.home, color: Colors.white),
                label: const Text(
                  'العودة للرئيسية',
                  style: TextStyle(
                    fontFamily: 'Cairo',
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                    color: Colors.white,
                  ),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(
                    0xFF0D47A1,
                  ), // لون أزرق غامق مطابق للسلاسل
                  padding: const EdgeInsets.symmetric(
                    horizontal: 20,
                    vertical: 12,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Question? _findQuestionById(List<Question> questions, int id) {
    try {
      return questions.firstWhere((q) => q.id == id);
    } catch (e) {
      return null;
    }
  }

  Widget _buildQuestionGridItem(
    BuildContext context,
    Question question,
    UserAnswer userAnswer,
    int questionNumber,
    int index,
    List<UserAnswer> allUserAnswers,
    List<Question> allQuestions,
  ) {
    // التحقق من اتجاه الشاشة
    final isLandscapeMode =
        MediaQuery.of(context).orientation == Orientation.landscape;

    if (isLandscapeMode) {
      // تصميم خاص للوضع الأفقي مع جزء سفلي للإجابة الصحيحة
      return InkWell(
        onTap: () {
          _showQuestionDetailsDialog(
            context,
            question,
            userAnswer,
            currentIndex: index,
            allUserAnswers: allUserAnswers,
            allQuestions: allQuestions,
          );
        },
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            // إضافة ظل للمربع
            boxShadow: const [
              BoxShadow(
                color: Color.fromRGBO(0, 0, 0, 0.2),
                spreadRadius: 1,
                blurRadius: 3,
                offset: Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            children: [
              // الجزء العلوي - رقم السؤال
              Expanded(
                child: Container(
                  width: double.infinity,
                  decoration: BoxDecoration(
                    color: userAnswer.isCorrect ? Colors.green : Colors.red,
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(8),
                      topRight: Radius.circular(8),
                    ),
                  ),
                  child: Stack(
                    children: [
                      // رقم السؤال في المنتصف
                      Center(
                        child: Text(
                          '$questionNumber',
                          style: TextStyle(
                            fontFamily: 'Cairo',
                            fontSize: MediaQuery.of(context).size.height /
                                25, // حجم خط ديناميكي أكبر
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                      ),

                      // معرف السؤال في الزاوية العلوية اليسرى
                      Positioned(
                        top: 2,
                        left: 4,
                        child: Text(
                          '${question.id}',
                          style: TextStyle(
                            fontFamily: 'Cairo',
                            fontSize: MediaQuery.of(context).size.height /
                                60, // حجم خط ديناميكي أكبر
                            fontWeight: FontWeight.bold,
                            color: Colors.white70,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              // الجزء السفلي - الإجابة الصحيحة
              Container(
                width: double.infinity,
                height: MediaQuery.of(context).size.height /
                    22, // ارتفاع ديناميكي للجزء السفلي
                decoration: const BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.only(
                    bottomLeft: Radius.circular(8),
                    bottomRight: Radius.circular(8),
                  ),
                ),
                child: Center(
                  child: Text(
                    // عرض الإجابات الصحيحة كأرقام بدون فلترة
                    question.correctAnswerIndices.join(' '),
                    style: TextStyle(
                      fontFamily: 'Cairo',
                      fontSize: MediaQuery.of(context).size.height /
                          30, // حجم خط ديناميكي أكبر
                      fontWeight: FontWeight.bold,
                      color: Colors.black,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    } else {
      // تصميم الوضع العمودي (الحالي)
      return InkWell(
        onTap: () {
          _showQuestionDetailsDialog(
            context,
            question,
            userAnswer,
            currentIndex: index,
            allUserAnswers: allUserAnswers,
            allQuestions: allQuestions,
          );
        },
        child: Container(
          decoration: BoxDecoration(
            color: userAnswer.isCorrect ? Colors.green : Colors.red,
            borderRadius: BorderRadius.circular(8),
            // إضافة ظل للمربع
            boxShadow: const [
              BoxShadow(
                color: Color.fromRGBO(0, 0, 0, 0.2),
                spreadRadius: 1,
                blurRadius: 3,
                offset: Offset(0, 2),
              ),
            ],
          ),
          child: Stack(
            children: [
              // رقم السؤال في المنتصف
              Center(
                child: Text(
                  '$questionNumber',
                  style: const TextStyle(
                    fontFamily: 'Cairo',
                    fontSize: 32, // حجم خط أكبر
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ),

              // معرف السؤال في الزاوية العلوية اليسرى
              Positioned(
                top: 2,
                left: 4,
                child: Text(
                  '${question.id}',
                  style: const TextStyle(
                    fontFamily: 'Cairo',
                    fontSize: 14, // حجم خط أكبر
                    fontWeight: FontWeight.bold,
                    color: Colors.white70,
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    }
  }

  // دالة لمشاركة نتيجة الاختبار
  void _shareResult(
    BuildContext context,
    int correctCount,
    int totalQuestions,
    String seriesTitle,
  ) async {
    // إنشاء نسبة النجاح
    final percentage = (correctCount / totalQuestions * 100).toStringAsFixed(0);

    try {
      // عرض رسالة نجاح للمستخدم
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'تم مشاركة النتيجة: $correctCount من أصل $totalQuestions (نسبة النجاح: $percentage%)',
              style: const TextStyle(fontFamily: 'Cairo'),
            ),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 3),
          ),
        );
      }

      debugPrint('تم مشاركة النتيجة بنجاح');
    } catch (e) {
      debugPrint('خطأ في مشاركة النتيجة: $e');
      // عرض رسالة خطأ للمستخدم
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text(
              'حدث خطأ أثناء مشاركة النتيجة',
              style: TextStyle(fontFamily: 'Cairo'),
            ),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 3),
          ),
        );
      }
    }
  }

  void _showQuestionDetailsDialog(
    BuildContext context,
    Question question,
    UserAnswer userAnswer, {
    int? currentIndex,
    List<UserAnswer>? allUserAnswers,
    List<Question>? allQuestions,
  }) {
    // التحقق من اتجاه الشاشة
    final isLandscapeMode =
        MediaQuery.of(context).orientation == Orientation.landscape;

    if (isLandscapeMode) {
      // تصميم خاص للوضع الأفقي - ملء الشاشة
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => Scaffold(
          backgroundColor: Colors.black, // تغيير لون خلفية الحوار إلى أسود
          appBar: AppBar(
            backgroundColor: Colors.black,
            automaticallyImplyLeading: false,
            toolbarHeight: 0, // إخفاء شريط العنوان تمامًا
            elevation: 0, // إزالة الظل
          ),
          body: SafeArea(
            child: Container(
              width: MediaQuery.of(context).size.width,
              height: MediaQuery.of(context).size.height,
              padding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 5,
              ), // تقليل الحشو العمودي
              color: Colors.black, // تغيير لون خلفية الحاوية إلى أسود
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // رقم السؤال فقط (دائرة زرقاء) في الأعلى في اليمين
                  Align(
                    alignment: Alignment.topRight,
                    child: Padding(
                      padding: const EdgeInsets.only(
                        bottom: 5,
                        top: 0,
                        right: 20,
                      ),
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 4,
                        ),
                        decoration: const BoxDecoration(
                          color: Colors.blue,
                          borderRadius: BorderRadius.all(
                            Radius.circular(8),
                          ),
                        ),
                        child: Text(
                          '${currentIndex != null ? currentIndex + 1 : ""}',
                          style: const TextStyle(
                            fontFamily: 'Cairo',
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                  ),

                  // صورة السؤال ومعلومات الإجابة
                  Expanded(
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // معلومات الإجابة (على اليسار)
                        Expanded(
                          flex: 2, // تقليل العرض النسبي
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              // مربعات الإجابات بجانب بعضها
                              Row(
                                children: [
                                  // إجابتك
                                  Expanded(
                                    child: Container(
                                      padding: const EdgeInsets.all(12),
                                      height: 120, // ارتفاع ثابت
                                      decoration: BoxDecoration(
                                        color: Colors.blue.shade100,
                                        borderRadius: BorderRadius.circular(
                                          8,
                                        ),
                                        border: Border.all(
                                          color: Colors.blue.shade300,
                                        ),
                                      ),
                                      child: Column(
                                        children: [
                                          const Icon(
                                            Icons.person,
                                            color: Colors.blue,
                                            size: 28,
                                          ),
                                          const SizedBox(height: 8),
                                          // أرقام الإجابات المختارة
                                          Expanded(
                                            child: SingleChildScrollView(
                                              child: Wrap(
                                                alignment: WrapAlignment.center,
                                                spacing:
                                                    8, // المسافة الأفقية بين الدوائر
                                                runSpacing:
                                                    8, // المسافة العمودية بين الصفوف
                                                children: userAnswer
                                                        .selectedAnswerIndices
                                                        .isEmpty
                                                    ? [
                                                        // إذا لم يختر المستخدم أي إجابة
                                                        Container(
                                                          width: 32,
                                                          height: 32,
                                                          decoration:
                                                              BoxDecoration(
                                                            color: Colors
                                                                .grey.shade400,
                                                            shape:
                                                                BoxShape.circle,
                                                          ),
                                                          child: const Center(
                                                            child: Text(
                                                              '-',
                                                              style: TextStyle(
                                                                fontFamily:
                                                                    'Cairo',
                                                                fontWeight:
                                                                    FontWeight
                                                                        .bold,
                                                                color: Colors
                                                                    .white,
                                                                fontSize: 16,
                                                              ),
                                                            ),
                                                          ),
                                                        ),
                                                      ]
                                                    : userAnswer
                                                        .selectedAnswerIndices
                                                        .map((
                                                        index,
                                                      ) {
                                                        // تحويل المؤشر إلى مؤشر يبدأ من 1 للعرض
                                                        final displayIndex =
                                                            index + 1;
                                                        return Container(
                                                          width: 32,
                                                          height: 32,
                                                          decoration:
                                                              const BoxDecoration(
                                                            color: Colors.blue,
                                                            shape:
                                                                BoxShape.circle,
                                                          ),
                                                          child: Center(
                                                            child: Text(
                                                              '$displayIndex',
                                                              style:
                                                                  const TextStyle(
                                                                fontFamily:
                                                                    'Cairo',
                                                                fontWeight:
                                                                    FontWeight
                                                                        .bold,
                                                                color: Colors
                                                                    .white,
                                                                fontSize: 16,
                                                              ),
                                                            ),
                                                          ),
                                                        );
                                                      }).toList(),
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),

                                  const SizedBox(width: 8),

                                  // الإجابة الصحيحة
                                  Expanded(
                                    child: Container(
                                      padding: const EdgeInsets.all(12),
                                      height: 120, // ارتفاع ثابت
                                      decoration: BoxDecoration(
                                        color: Colors.green.shade100,
                                        borderRadius: BorderRadius.circular(
                                          8,
                                        ),
                                        border: Border.all(
                                          color: Colors.green.shade300,
                                        ),
                                      ),
                                      child: Column(
                                        children: [
                                          const Icon(
                                            Icons.check_circle,
                                            color: Colors.green,
                                            size: 28,
                                          ),
                                          const SizedBox(height: 8),
                                          // أرقام الإجابات الصحيحة
                                          Expanded(
                                            child: SingleChildScrollView(
                                              child: Wrap(
                                                alignment: WrapAlignment.center,
                                                spacing:
                                                    8, // المسافة الأفقية بين الدوائر
                                                runSpacing:
                                                    8, // المسافة العمودية بين الصفوف
                                                children: question
                                                        .correctAnswerIndices
                                                        .isEmpty
                                                    ? [
                                                        // إذا لم تكن هناك إجابات صحيحة
                                                        Container(
                                                          width: 32,
                                                          height: 32,
                                                          decoration:
                                                              BoxDecoration(
                                                            color: Colors
                                                                .grey.shade400,
                                                            shape:
                                                                BoxShape.circle,
                                                          ),
                                                          child: const Center(
                                                            child: Text(
                                                              '-',
                                                              style: TextStyle(
                                                                fontFamily:
                                                                    'Cairo',
                                                                fontWeight:
                                                                    FontWeight
                                                                        .bold,
                                                                color: Colors
                                                                    .white,
                                                                fontSize: 16,
                                                              ),
                                                            ),
                                                          ),
                                                        ),
                                                      ]
                                                    : question
                                                        .correctAnswerIndices
                                                        .map((
                                                        index,
                                                      ) {
                                                        // تحقق مما إذا كان المستخدم قد اختار هذه الإجابة
                                                        final isSelected = userAnswer
                                                            .selectedAnswerIndices
                                                            .map(
                                                              (i) => i + 1,
                                                            ) // تحويل من 0-based إلى 1-based
                                                            .contains(
                                                              index,
                                                            );

                                                        return Container(
                                                          width: 32,
                                                          height: 32,
                                                          decoration:
                                                              BoxDecoration(
                                                            color: Colors.green,
                                                            shape:
                                                                BoxShape.circle,
                                                            // إضافة حدود بيضاء للإجابات التي اختارها المستخدم بشكل صحيح
                                                            border: isSelected
                                                                ? Border.all(
                                                                    color: Colors
                                                                        .white,
                                                                    width: 2,
                                                                  )
                                                                : null,
                                                          ),
                                                          child: Center(
                                                            child: Text(
                                                              '$index',
                                                              style:
                                                                  const TextStyle(
                                                                fontFamily:
                                                                    'Cairo',
                                                                fontWeight:
                                                                    FontWeight
                                                                        .bold,
                                                                color: Colors
                                                                    .white,
                                                                fontSize: 16,
                                                              ),
                                                            ),
                                                          ),
                                                        );
                                                      }).toList(),
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ],
                              ),

                              const SizedBox(height: 16),

                              // مربع الشرح (يظهر لجميع الأسئلة)
                              if (question.explanation.isNotEmpty) ...[
                                const SizedBox(height: 16),
                                Container(
                                  width: double.infinity,
                                  padding: const EdgeInsets.all(12),
                                  decoration: BoxDecoration(
                                    color: userAnswer.isCorrect
                                        ? Colors.blue.shade50
                                        : Colors.red.shade50,
                                    borderRadius: BorderRadius.circular(8),
                                    border: Border.all(
                                      color: userAnswer.isCorrect
                                          ? Colors.blue.shade200
                                          : Colors.red.shade200,
                                    ),
                                  ),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      // عنوان الشرح
                                      Row(
                                        children: [
                                          Icon(
                                            Icons.info_outline,
                                            color: userAnswer.isCorrect
                                                ? Colors.blue
                                                : Colors.red,
                                            size: 24,
                                          ),
                                          const SizedBox(width: 8),
                                          Text(
                                            'الشرح:',
                                            style: TextStyle(
                                              fontFamily: 'Cairo',
                                              fontWeight: FontWeight.bold,
                                              color: userAnswer.isCorrect
                                                  ? Colors.blue
                                                  : Colors.red,
                                              fontSize: 16,
                                            ),
                                          ),
                                        ],
                                      ),
                                      const SizedBox(height: 8),
                                      // نص الشرح
                                      Text(
                                        question.explanation,
                                        style: const TextStyle(
                                          fontFamily: 'Cairo',
                                          fontSize: 14,
                                        ),
                                        textAlign: TextAlign.right,
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ],
                          ),
                        ),

                        const SizedBox(width: 16),

                        // صورة السؤال (على اليمين)
                        Expanded(
                          flex: 5, // زيادة العرض النسبي أكثر
                          child: Container(
                            // استخدام ارتفاع كامل للشاشة مع هامش صغير
                            height: MediaQuery.of(context).size.height - 100,
                            // إزالة القيود على الارتفاع
                            margin: const EdgeInsets.symmetric(vertical: 5),
                            decoration: BoxDecoration(
                              border: Border.all(
                                color: Colors.grey.shade300,
                                width: 1,
                              ), // تقليل سمك الحدود
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(8),
                              child: Stack(
                                children: [
                                  // صورة السؤال مع إمكانية التكبير والتحريك
                                  Positioned.fill(
                                    child: GestureDetector(
                                      onDoubleTap: () {
                                        // عرض الصورة في وضع ملء الشاشة مع إمكانية التكبير والتحريك
                                        showDialog(
                                          context: context,
                                          builder: (context) => Dialog(
                                            insetPadding: EdgeInsets.zero,
                                            backgroundColor: Colors.transparent,
                                            child: Stack(
                                              children: [
                                                // الصورة مع إمكانية التكبير والتحريك
                                                PhotoView(
                                                  imageProvider: question
                                                          .imageUrl
                                                          .startsWith(
                                                    'assets/',
                                                  )
                                                      ? AssetImage(
                                                          question.imageUrl,
                                                        )
                                                      : NetworkImage(
                                                          question.imageUrl,
                                                        ) as ImageProvider,
                                                  minScale:
                                                      PhotoViewComputedScale
                                                          .contained,
                                                  maxScale:
                                                      PhotoViewComputedScale
                                                              .covered *
                                                          2,
                                                  backgroundDecoration:
                                                      const BoxDecoration(
                                                    color: Colors.black87,
                                                  ),
                                                  loadingBuilder: (
                                                    context,
                                                    event,
                                                  ) =>
                                                      Center(
                                                    child:
                                                        CircularProgressIndicator(
                                                      value: event == null
                                                          ? 0
                                                          : event.cumulativeBytesLoaded /
                                                              (event.expectedTotalBytes ??
                                                                  1),
                                                    ),
                                                  ),
                                                  errorBuilder: (
                                                    context,
                                                    error,
                                                    stackTrace,
                                                  ) {
                                                    return Center(
                                                      child: Column(
                                                        mainAxisAlignment:
                                                            MainAxisAlignment
                                                                .center,
                                                        children: [
                                                          Icon(
                                                            Icons.broken_image,
                                                            size: 48,
                                                            color: Colors
                                                                .grey[400],
                                                          ),
                                                          const SizedBox(
                                                            height: 8,
                                                          ),
                                                          const Text(
                                                            'تعذر تحميل الصورة',
                                                            style: TextStyle(
                                                              fontFamily:
                                                                  'Cairo',
                                                              fontSize: 14,
                                                              color:
                                                                  Colors.grey,
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                    );
                                                  },
                                                ),

                                                // زر الإغلاق
                                                Positioned(
                                                  top: 20,
                                                  right: 20,
                                                  child: Container(
                                                    decoration:
                                                        const BoxDecoration(
                                                      color: Colors.red,
                                                      shape: BoxShape.circle,
                                                    ),
                                                    child: IconButton(
                                                      icon: const Icon(
                                                        Icons.close,
                                                        color: Colors.white,
                                                      ),
                                                      onPressed: () =>
                                                          Navigator.pop(
                                                        context,
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                        );
                                      },
                                      child: question.imageUrl.startsWith(
                                        'assets/',
                                      )
                                          ? Image.asset(
                                              question.imageUrl,
                                              fit: BoxFit.contain,
                                              width: double.infinity,
                                              height: double.infinity,
                                              errorBuilder: (
                                                context,
                                                error,
                                                stackTrace,
                                              ) {
                                                return Center(
                                                  child: Column(
                                                    mainAxisAlignment:
                                                        MainAxisAlignment
                                                            .center,
                                                    children: [
                                                      Icon(
                                                        Icons.broken_image,
                                                        size: 48,
                                                        color: Colors.grey[400],
                                                      ),
                                                      const SizedBox(
                                                        height: 8,
                                                      ),
                                                      const Text(
                                                        'تعذر تحميل الصورة',
                                                        style: TextStyle(
                                                          fontFamily: 'Cairo',
                                                          fontSize: 14,
                                                          color: Colors.grey,
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                );
                                              },
                                            )
                                          : Image.network(
                                              question.imageUrl,
                                              fit: BoxFit.contain,
                                              width: double.infinity,
                                              height: double.infinity,
                                              errorBuilder: (
                                                context,
                                                error,
                                                stackTrace,
                                              ) {
                                                return Center(
                                                  child: Column(
                                                    mainAxisAlignment:
                                                        MainAxisAlignment
                                                            .center,
                                                    children: [
                                                      Icon(
                                                        Icons.broken_image,
                                                        size: 48,
                                                        color: Colors.grey[400],
                                                      ),
                                                      const SizedBox(
                                                        height: 8,
                                                      ),
                                                      const Text(
                                                        'تعذر تحميل الصورة',
                                                        style: TextStyle(
                                                          fontFamily: 'Cairo',
                                                          fontSize: 14,
                                                          color: Colors.grey,
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                );
                                              },
                                            ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),

                  // أزرار التنقل والإغلاق
                  Padding(
                    padding: const EdgeInsets.only(
                      top: 5,
                      bottom: 15,
                    ),
                    child: Row(
                      mainAxisAlignment:
                          MainAxisAlignment.center, // تغيير المحاذاة إلى الوسط
                      children: [
                        // زر السابق
                        if (currentIndex != null && currentIndex > 0)
                          Container(
                            margin: const EdgeInsets.symmetric(
                              horizontal: 8,
                            ),
                            decoration: const BoxDecoration(
                              borderRadius: BorderRadius.all(
                                Radius.circular(8),
                              ),
                              gradient: LinearGradient(
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                                colors: [
                                  Color(0xFF1565C0), // أزرق غامق
                                  Color(0xFF0D47A1), // أزرق غامق أكثر
                                ],
                              ),
                            ),
                            child: IconButton(
                              onPressed: () {
                                // إغلاق النافذة الحالية
                                Navigator.pop(context);

                                // عرض السؤال السابق
                                if (allUserAnswers != null &&
                                    allQuestions != null &&
                                    currentIndex > 0) {
                                  final prevIndex = currentIndex - 1;
                                  final prevUserAnswer =
                                      allUserAnswers[prevIndex];
                                  final prevQuestion = _findQuestionById(
                                    allQuestions,
                                    prevUserAnswer.questionId,
                                  );

                                  if (prevQuestion != null) {
                                    _showQuestionDetailsDialog(
                                      context,
                                      prevQuestion,
                                      prevUserAnswer,
                                      currentIndex: prevIndex,
                                      allUserAnswers: allUserAnswers,
                                      allQuestions: allQuestions,
                                    );
                                  }
                                }
                              },
                              icon: const Icon(
                                Icons.arrow_back,
                                color: Colors.white,
                                size: 24,
                              ),
                              tooltip: 'السابق',
                            ),
                          ),

                        // زر الإغلاق
                        Container(
                          margin: const EdgeInsets.symmetric(horizontal: 8),
                          decoration: const BoxDecoration(
                            borderRadius: BorderRadius.all(
                              Radius.circular(8),
                            ),
                            gradient: LinearGradient(
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                              colors: [
                                Color(0xFFE53935), // أحمر فاتح
                                Color(0xFFB71C1C), // أحمر غامق
                              ],
                            ),
                          ),
                          child: IconButton(
                            onPressed: () => Navigator.pop(context),
                            icon: const Icon(
                              Icons.close,
                              color: Colors.white,
                              size: 30,
                            ),
                            tooltip: 'إغلاق',
                          ),
                        ),

                        // زر التالي
                        if (currentIndex != null &&
                            allUserAnswers != null &&
                            currentIndex < allUserAnswers.length - 1)
                          Container(
                            margin: const EdgeInsets.symmetric(
                              horizontal: 8,
                            ),
                            decoration: const BoxDecoration(
                              borderRadius: BorderRadius.all(
                                Radius.circular(8),
                              ),
                              gradient: LinearGradient(
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                                colors: [
                                  Color(0xFF1565C0), // أزرق غامق
                                  Color(0xFF0D47A1), // أزرق غامق أكثر
                                ],
                              ),
                            ),
                            child: IconButton(
                              onPressed: () {
                                // إغلاق النافذة الحالية
                                Navigator.pop(context);

                                // عرض السؤال التالي
                                if (currentIndex < allUserAnswers.length - 1) {
                                  final nextIndex = currentIndex + 1;
                                  final nextUserAnswer =
                                      allUserAnswers[nextIndex];
                                  final nextQuestion = allQuestions != null
                                      ? _findQuestionById(
                                          allQuestions,
                                          nextUserAnswer.questionId,
                                        )
                                      : null;

                                  if (nextQuestion != null) {
                                    _showQuestionDetailsDialog(
                                      context,
                                      nextQuestion,
                                      nextUserAnswer,
                                      currentIndex: nextIndex,
                                      allUserAnswers: allUserAnswers,
                                      allQuestions: allQuestions,
                                    );
                                  }
                                }
                              },
                              icon: const Icon(
                                Icons.arrow_forward,
                                color: Colors.white,
                                size: 24,
                              ),
                              tooltip: 'التالي',
                            ),
                          ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      );
    } else {
      // تصميم الوضع العمودي (الحالي)
      showDialog(
        context: context,
        builder: (context) => Dialog(
          insetPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 24,
          ),
          backgroundColor: Colors.black, // تغيير لون خلفية الحوار إلى أسود
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            color: Colors.black, // تغيير لون خلفية الحاوية إلى أسود
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // رقم السؤال في مستطيل أزرق في الأعلى
                Align(
                  alignment: Alignment.topCenter,
                  child: Padding(
                    padding: const EdgeInsets.only(bottom: 16),
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 8,
                      ),
                      decoration: const BoxDecoration(
                        color: Colors.blue,
                        borderRadius: BorderRadius.all(Radius.circular(8)),
                      ),
                      child: Text(
                        '${currentIndex != null ? currentIndex + 1 : ""}',
                        style: const TextStyle(
                          fontFamily: 'Cairo',
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ),
                ),

                // مسافة بعد رقم السؤال
                const SizedBox(height: 8),

                // صورة السؤال
                Flexible(
                  child: Container(
                    width: double.infinity,
                    constraints: const BoxConstraints(
                      minHeight: 100,
                      maxHeight: 250,
                    ),
                    margin: const EdgeInsets.only(bottom: 16),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey.shade300),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(8),
                      child: question.imageUrl.startsWith('assets/')
                          ? Image.asset(
                              question.imageUrl,
                              fit: BoxFit.contain,
                              errorBuilder: (context, error, stackTrace) {
                                return Center(
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Icon(
                                        Icons.broken_image,
                                        size: 48,
                                        color: Colors.grey[400],
                                      ),
                                      const SizedBox(height: 8),
                                      const Text(
                                        'تعذر تحميل الصورة',
                                        style: TextStyle(
                                          fontFamily: 'Cairo',
                                          fontSize: 14,
                                          color: Colors.grey,
                                        ),
                                      ),
                                    ],
                                  ),
                                );
                              },
                            )
                          : Image.network(
                              question.imageUrl,
                              fit: BoxFit.contain,
                              errorBuilder: (context, error, stackTrace) {
                                return Center(
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Icon(
                                        Icons.broken_image,
                                        size: 48,
                                        color: Colors.grey[400],
                                      ),
                                      const SizedBox(height: 8),
                                      const Text(
                                        'تعذر تحميل الصورة',
                                        style: TextStyle(
                                          fontFamily: 'Cairo',
                                          fontSize: 14,
                                          color: Colors.grey,
                                        ),
                                      ),
                                    ],
                                  ),
                                );
                              },
                            ),
                    ),
                  ),
                ),

                // عرض الإجابات في صف واحد
                IntrinsicHeight(
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      // إجابة المستخدم
                      Expanded(
                        child: Container(
                          margin: const EdgeInsets.only(right: 8),
                          padding: const EdgeInsets.all(12),
                          height: 120, // ارتفاع ثابت
                          decoration: BoxDecoration(
                            color: Colors.blue.shade100,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: Colors.blue.shade300),
                          ),
                          child: Column(
                            children: [
                              // أيقونة فقط
                              const Icon(
                                Icons.person,
                                color: Colors.blue,
                                size: 28,
                              ),
                              const SizedBox(height: 8),

                              // أرقام الإجابات في صفوف
                              Expanded(
                                child: SingleChildScrollView(
                                  child: Wrap(
                                    alignment: WrapAlignment.center,
                                    spacing: 8, // المسافة الأفقية بين الدوائر
                                    runSpacing:
                                        8, // المسافة العمودية بين الصفوف
                                    children: userAnswer
                                            .selectedAnswerIndices.isEmpty
                                        ? [
                                            // إذا لم يختر المستخدم أي إجابة
                                            Container(
                                              width: 32,
                                              height: 32,
                                              decoration: BoxDecoration(
                                                color: Colors.grey.shade400,
                                                shape: BoxShape.circle,
                                              ),
                                              child: const Center(
                                                child: Text(
                                                  '-',
                                                  style: TextStyle(
                                                    fontFamily: 'Cairo',
                                                    fontWeight: FontWeight.bold,
                                                    color: Colors.white,
                                                    fontSize: 16,
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ]
                                        : userAnswer.selectedAnswerIndices
                                            .map((index) {
                                            // تحويل المؤشر إلى مؤشر يبدأ من 1 للعرض
                                            final displayIndex = index + 1;
                                            return Container(
                                              width: 32,
                                              height: 32,
                                              decoration: const BoxDecoration(
                                                color: Colors.blue,
                                                shape: BoxShape.circle,
                                              ),
                                              child: Center(
                                                child: Text(
                                                  '$displayIndex',
                                                  style: const TextStyle(
                                                    fontFamily: 'Cairo',
                                                    fontWeight: FontWeight.bold,
                                                    color: Colors.white,
                                                    fontSize: 16,
                                                  ),
                                                ),
                                              ),
                                            );
                                          }).toList(),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),

                      // الإجابة الصحيحة
                      Expanded(
                        child: Container(
                          margin: const EdgeInsets.only(left: 8),
                          padding: const EdgeInsets.all(12),
                          height: 120, // ارتفاع ثابت
                          decoration: BoxDecoration(
                            color: Colors.green.shade100,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              color: Colors.green.shade300,
                            ),
                          ),
                          child: Column(
                            children: [
                              // أيقونة فقط
                              const Icon(
                                Icons.check_circle,
                                color: Colors.green,
                                size: 28,
                              ),
                              const SizedBox(height: 8),

                              // أرقام الإجابات في صفوف
                              Expanded(
                                child: SingleChildScrollView(
                                  child: Wrap(
                                    alignment: WrapAlignment.center,
                                    spacing: 8, // المسافة الأفقية بين الدوائر
                                    runSpacing:
                                        8, // المسافة العمودية بين الصفوف
                                    children: question
                                            .correctAnswerIndices.isEmpty
                                        ? [
                                            // إذا لم تكن هناك إجابات صحيحة
                                            Container(
                                              width: 32,
                                              height: 32,
                                              decoration: BoxDecoration(
                                                color: Colors.grey.shade400,
                                                shape: BoxShape.circle,
                                              ),
                                              child: const Center(
                                                child: Text(
                                                  '-',
                                                  style: TextStyle(
                                                    fontFamily: 'Cairo',
                                                    fontWeight: FontWeight.bold,
                                                    color: Colors.white,
                                                    fontSize: 16,
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ]
                                        : question.correctAnswerIndices.map((
                                            index,
                                          ) {
                                            // تحقق مما إذا كان المستخدم قد اختار هذه الإجابة
                                            final isSelected = userAnswer
                                                .selectedAnswerIndices
                                                .map((i) => i + 1)
                                                .contains(index);

                                            return Container(
                                              width: 32,
                                              height: 32,
                                              decoration: BoxDecoration(
                                                color: Colors.green,
                                                shape: BoxShape.circle,
                                                // إضافة حدود بيضاء للإجابات التي اختارها المستخدم بشكل صحيح
                                                border: isSelected
                                                    ? Border.all(
                                                        color: Colors.white,
                                                        width: 2,
                                                      )
                                                    : null,
                                              ),
                                              child: Center(
                                                child: Text(
                                                  '$index',
                                                  style: const TextStyle(
                                                    fontFamily: 'Cairo',
                                                    fontWeight: FontWeight.bold,
                                                    color: Colors.white,
                                                    fontSize: 16,
                                                  ),
                                                ),
                                              ),
                                            );
                                          }).toList(),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                // مربع الشرح (يظهر لجميع الأسئلة)
                if (question.explanation.isNotEmpty) ...[
                  const SizedBox(height: 16),
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: userAnswer.isCorrect
                          ? Colors.blue.shade50
                          : Colors.red.shade50,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: userAnswer.isCorrect
                            ? Colors.blue.shade200
                            : Colors.red.shade200,
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // عنوان الشرح
                        Row(
                          children: [
                            Icon(
                              Icons.info_outline,
                              color: userAnswer.isCorrect
                                  ? Colors.blue
                                  : Colors.red,
                              size: 24,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              'الشرح:',
                              style: TextStyle(
                                fontFamily: 'Cairo',
                                fontWeight: FontWeight.bold,
                                color: userAnswer.isCorrect
                                    ? Colors.blue
                                    : Colors.red,
                                fontSize: 16,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        // نص الشرح
                        Text(
                          question.explanation,
                          style: const TextStyle(
                            fontFamily: 'Cairo',
                            fontSize: 14,
                          ),
                          textAlign: TextAlign.right,
                        ),
                      ],
                    ),
                  ),
                ],

                // أزرار التنقل
                Padding(
                  padding: const EdgeInsets.only(top: 16),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // زر السابق
                      if (currentIndex != null && currentIndex > 0)
                        Container(
                          margin: const EdgeInsets.symmetric(horizontal: 8),
                          decoration: const BoxDecoration(
                            color: Colors.blue,
                            shape: BoxShape.circle,
                          ),
                          child: IconButton(
                            onPressed: () {
                              // إغلاق النافذة الحالية
                              Navigator.pop(context);

                              // عرض السؤال السابق
                              if (allUserAnswers != null &&
                                  allQuestions != null &&
                                  currentIndex > 0) {
                                final prevIndex = currentIndex - 1;
                                final prevUserAnswer =
                                    allUserAnswers[prevIndex];
                                final prevQuestion = _findQuestionById(
                                  allQuestions,
                                  prevUserAnswer.questionId,
                                );

                                if (prevQuestion != null) {
                                  _showQuestionDetailsDialog(
                                    context,
                                    prevQuestion,
                                    prevUserAnswer,
                                    currentIndex: prevIndex,
                                    allUserAnswers: allUserAnswers,
                                    allQuestions: allQuestions,
                                  );
                                }
                              }
                            },
                            icon: const Icon(
                              Icons.arrow_back_ios,
                              color: Colors.white,
                              size: 24,
                            ),
                            tooltip: 'السابق',
                          ),
                        ),

                      // زر الإغلاق
                      Container(
                        margin: const EdgeInsets.symmetric(horizontal: 8),
                        decoration: const BoxDecoration(
                          color: Colors.red,
                          shape: BoxShape.circle,
                        ),
                        child: IconButton(
                          onPressed: () => Navigator.pop(context),
                          icon: const Icon(
                            Icons.close,
                            color: Colors.white,
                            size: 30,
                          ),
                          tooltip: 'إغلاق',
                        ),
                      ),

                      // زر التالي
                      if (currentIndex != null &&
                          allUserAnswers != null &&
                          currentIndex < allUserAnswers.length - 1)
                        Container(
                          margin: const EdgeInsets.symmetric(horizontal: 8),
                          decoration: const BoxDecoration(
                            color: Colors.blue,
                            shape: BoxShape.circle,
                          ),
                          child: IconButton(
                            onPressed: () {
                              // إغلاق النافذة الحالية
                              Navigator.pop(context);

                              // عرض السؤال التالي
                              if (currentIndex < allUserAnswers.length - 1) {
                                final nextIndex = currentIndex + 1;
                                final nextUserAnswer =
                                    allUserAnswers[nextIndex];
                                final nextQuestion = allQuestions != null
                                    ? _findQuestionById(
                                        allQuestions,
                                        nextUserAnswer.questionId,
                                      )
                                    : null;

                                if (nextQuestion != null) {
                                  _showQuestionDetailsDialog(
                                    context,
                                    nextQuestion,
                                    nextUserAnswer,
                                    currentIndex: nextIndex,
                                    allUserAnswers: allUserAnswers,
                                    allQuestions: allQuestions,
                                  );
                                }
                              }
                            },
                            icon: const Icon(
                              Icons.arrow_forward_ios,
                              color: Colors.white,
                              size: 24,
                            ),
                            tooltip: 'التالي',
                          ),
                        ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    }
  }
}
