import 'package:flutter/material.dart';

class ViolationsPage extends StatelessWidget {
  const ViolationsPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // قائمة بمعلومات المخالفات
    final List<Map<String, dynamic>> violations = [
      {
        'image': 'assets/images/vehicles/documents/doc1.png',
        'description': 'مخالفات السرعة',
      },
      {
        'image': 'assets/images/vehicles/documents/doc2.png',
        'description': 'مخالفات الوقوف',
      },
      {
        'image': 'assets/images/vehicles/documents/doc3.png',
        'description': 'مخالفات الإشارات',
      },
      {
        'image': 'assets/images/vehicles/documents/doc4.png',
        'description': 'مخالفات الوثائق',
      },
    ];

    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'المخالفات',
          style: TextStyle(
            fontFamily: 'Cairo',
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: const Color(0xFF2196F3),
        centerTitle: true,
        // إزالة leading وإضافة زر العودة في actions على اليمين
        automaticallyImplyLeading: false, // إلغاء زر العودة التلقائي
        actions: [
          IconButton(
            icon: const Icon(Icons.arrow_forward), // سهم للأمام (يمين) للعودة
            onPressed: () {
              Navigator.pop(context);
            },
          ),
        ],
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.blue.shade400,
              Colors.grey.shade800,
            ],
          ),
        ),
        child: OrientationBuilder(
          builder: (context, orientation) {
            // التحقق من اتجاه الشاشة لتحديد عدد الأعمدة
            final isLandscape = orientation == Orientation.landscape;
            
            return GridView.builder(
              padding: const EdgeInsets.all(16),
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: isLandscape ? 2 : 1, // عمودان في الوضع الأفقي، عمود واحد في الوضع الرأسي
                childAspectRatio: 1, // نسبة العرض إلى الارتفاع 1:1 للحفاظ على الشكل المربع
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
              ),
              itemCount: violations.length,
              itemBuilder: (context, index) {
                return _buildViolationCard(
                  context,
                  imagePath: violations[index]['image'],
                  description: violations[index]['description'],
                );
              },
            );
          },
        ),
      ),
    );
  }

  // دالة لبناء بطاقة المخالفة
  Widget _buildViolationCard(
    BuildContext context, {
    required String imagePath,
    required String description,
  }) {
    // حساب عرض البطاقة بناءً على عرض الشاشة
    // استخدام نسبة من عرض الشاشة مع هوامش
    final screenWidth = MediaQuery.of(context).size.width;
    final cardSize = screenWidth > 600 
        ? screenWidth * 0.4  // للشاشات الكبيرة
        : screenWidth * 0.8; // للشاشات الصغيرة
    
    // التأكد من أن البطاقة مربعة
    final cardWidth = cardSize;
    final cardHeight = cardSize;
    
    return Center(
      child: Card(
        margin: const EdgeInsets.only(bottom: 16),
        elevation: 4,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        child: AspectRatio(
          aspectRatio: 1, // نسبة العرض إلى الارتفاع 1:1 للحفاظ على الشكل المربع
          child: ClipRRect(
            borderRadius: BorderRadius.circular(12),
            child: Image.asset(
              imagePath,
              width: cardWidth,
              height: cardHeight,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                // في حالة حدوث خطأ في تحميل الصورة
                return Container(
                  width: cardWidth,
                  height: cardHeight,
                  color: Colors.grey.shade200,
                  child: const Center(
                    child: Icon(
                      Icons.image_not_supported,
                      size: 50,
                      color: Colors.grey,
                    ),
                  ),
                );
              },
            ),
          ),
        ),
      ),
    );
  }
}
