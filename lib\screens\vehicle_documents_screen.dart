import 'package:flutter/material.dart';

class VehicleDocumentsScreen extends StatefulWidget {
  const VehicleDocumentsScreen({Key? key}) : super(key: key);

  @override
  State<VehicleDocumentsScreen> createState() => _VehicleDocumentsScreenState();
}

class _VehicleDocumentsScreenState extends State<VehicleDocumentsScreen> {
  // قائمة بمعلومات وثائق المركبة
  final List<Map<String, dynamic>> _vehicleDocuments = [
    {
      'image': 'assets/images/vehicles/documents/doc1.png',
      'description': 'رخصة السياقة',
    },
    {
      'image': 'assets/images/vehicles/documents/doc2.png',
      'description': 'البطاقة الرمادية',
    },
    {
      'image': 'assets/images/vehicles/documents/doc3.png',
      'description': 'شهادة التأمين',
    },
    {
      'image': 'assets/images/vehicles/documents/doc4.png',
      'description': 'الفحص التقني',
    },
    {
      'image': 'assets/images/vehicles/documents/doc5.png',
      'description': 'الضريبة السنوية',
    },
    {
      'image': 'assets/images/vehicles/documents/doc6.png',
      'description': 'رخصة النقل',
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'وثائق المركبة',
          style: TextStyle(
            fontFamily: 'Cairo',
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: const Color(0xFF2196F3),
        centerTitle: true,
        // إزالة leading وإضافة زر العودة في actions على اليمين
        automaticallyImplyLeading: false, // إلغاء زر العودة التلقائي
        actions: [
          IconButton(
            icon: const Icon(Icons.arrow_forward), // سهم للأمام (يمين) للعودة
            onPressed: () {
              Navigator.pop(context);
            },
          ),
        ],
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.blue.shade400,
              Colors.grey.shade800,
            ],
          ),
        ),
        child: OrientationBuilder(
          builder: (context, orientation) {
            // التحقق من اتجاه الشاشة لتحديد عدد الأعمدة
            final isLandscape = orientation == Orientation.landscape;

            return GridView.builder(
              padding: const EdgeInsets.all(16),
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: isLandscape
                    ? 2
                    : 1, // عمودان في الوضع الأفقي، عمود واحد في الوضع الرأسي
                childAspectRatio:
                    1, // نسبة العرض إلى الارتفاع 1:1 للحفاظ على الشكل المربع
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
              ),
              itemCount: _vehicleDocuments.length,
              itemBuilder: (context, index) {
                return _buildDocumentCard(
                  imagePath: _vehicleDocuments[index]['image'],
                  description: _vehicleDocuments[index]['description'],
                );
              },
            );
          },
        ),
      ),
    );
  }

  // دالة لبناء بطاقة وثيقة المركبة
  Widget _buildDocumentCard({
    required String imagePath,
    required String description,
  }) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // حساب عرض البطاقة بناءً على عرض الشاشة
        // استخدام نسبة من عرض الشاشة مع هوامش
        final screenWidth = MediaQuery.of(context).size.width;
        final cardSize = screenWidth > 600
            ? screenWidth * 0.4 // للشاشات الكبيرة
            : screenWidth * 0.8; // للشاشات الصغيرة

        // التأكد من أن البطاقة مربعة
        final cardWidth = cardSize;
        final cardHeight = cardSize;

        return Center(
          child: Card(
            margin: const EdgeInsets.only(bottom: 16),
            elevation: 4,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            child: AspectRatio(
              aspectRatio:
                  1, // نسبة العرض إلى الارتفاع 1:1 للحفاظ على الشكل المربع
              child: ClipRRect(
                borderRadius: BorderRadius.circular(12),
                child: Image.asset(
                  imagePath,
                  width: cardWidth,
                  height: cardHeight,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    // في حالة حدوث خطأ في تحميل الصورة
                    return Container(
                      width: cardWidth,
                      height: cardHeight,
                      color: Colors.grey.shade200,
                      child: const Center(
                        child: Icon(
                          Icons.image_not_supported,
                          size: 50,
                          color: Colors.grey,
                        ),
                      ),
                    );
                  },
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
