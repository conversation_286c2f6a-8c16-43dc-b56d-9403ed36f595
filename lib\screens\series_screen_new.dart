import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/series.dart';
import '../providers/series_provider.dart';
import '../providers/quiz_provider.dart';
import '../services/series_data_service_new.dart';
import '../services/connectivity_service.dart';
import 'question_screen_new.dart';
import 'settings_screen.dart';
import 'channels_screen.dart';
import 'contact_screen.dart';
import 'review_screen.dart';
import 'main_menu_screen.dart';

class SeriesScreenNew extends StatefulWidget {
  const SeriesScreenNew({Key? key}) : super(key: key);

  @override
  State<SeriesScreenNew> createState() => _SeriesScreenNewState();
}

class _SeriesScreenNewState extends State<SeriesScreenNew> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  final ConnectivityService _connectivityService = ConnectivityService();

  // دالة للتحقق من اتجاه الشاشة (أفقي أو عمودي)
  bool isLandscape(BuildContext context) {
    return MediaQuery.of(context).orientation == Orientation.landscape;
  }

  @override
  void initState() {
    super.initState();
    // تحميل البيانات مسبقًا
    _preloadData();

    // تأخير التحقق من الاتصال حتى يتم بناء الواجهة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _connectivityService.startMonitoring(context);
    });
  }

  @override
  void dispose() {
    _connectivityService.stopMonitoring();
    super.dispose();
  }

  // دالة لتحميل البيانات مسبقًا
  Future<void> _preloadData() async {
    // الحصول على مزود السلاسل
    final seriesProvider = Provider.of<SeriesProvider>(context, listen: false);

    // الحصول على مزود الاختبار
    final quizProvider = Provider.of<QuizProvider>(context, listen: false);

    // تحميل السلاسل إذا لم تكن محملة بالفعل
    if (seriesProvider.series.isEmpty) {
      await seriesProvider.refreshSeries();
    }

    // تهيئة الاختبار مسبقًا للسلسلة الأولى
    if (seriesProvider.series.isNotEmpty) {
      // تهيئة الاختبار بدون بدء فعلي
      quizProvider.preloadSeries(seriesProvider.series.first);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: _scaffoldKey,
      extendBodyBehindAppBar: true, // تمديد الخلفية خلف شريط التطبيق
      appBar: AppBar(
        title: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // شعار التطبيق
            Image.asset(
              'assets/images/logo.png',
              width: 40,
              height: 40,
              fit: BoxFit.contain,
            ),
            const SizedBox(width: 10),
            // نص العنوان
            const Text(
              'سياقة',
              style: TextStyle(
                fontFamily: 'Cairo',
                fontWeight: FontWeight.bold,
                color: Colors.white,
                shadows: [
                  Shadow(
                    offset: Offset(1.0, 1.0),
                    blurRadius: 3.0,
                    color: Color.fromARGB(150, 0, 0, 0),
                  ),
                ],
              ),
            ),
          ],
        ),
        backgroundColor: Colors.transparent, // جعل الشريط شفافًا
        elevation: 0, // إزالة الظل
        centerTitle: true,
        // إضافة تأثير زجاجي للشريط
        flexibleSpace: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Color.fromRGBO(0, 0, 0, 0.4),
                Color.fromRGBO(0, 0, 0, 0.1),
              ],
            ),
          ),
        ),
        leading: PopupMenuButton<String>(
          icon: const Icon(
            Icons.menu,
            color: Colors.white,
            shadows: [
              Shadow(
                offset: Offset(1.0, 1.0),
                blurRadius: 3.0,
                color: Color.fromARGB(150, 0, 0, 0),
              ),
            ],
          ),
          onSelected: (value) {
            if (value == 'settings') {
              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => const SettingsScreen()),
              );
            } else if (value == 'channels') {
              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => const ChannelsScreen()),
              );
            } else if (value == 'contact') {
              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => const ContactScreen()),
              );
            }
          },
          itemBuilder: (BuildContext context) => <PopupMenuEntry<String>>[
            const PopupMenuItem<String>(
              value: 'settings',
              child: Row(
                children: [
                  Icon(Icons.settings, color: Color(0xFF0D47A1)),
                  SizedBox(width: 8),
                  Text('الإعدادات'),
                ],
              ),
            ),
            const PopupMenuItem<String>(
              value: 'channels',
              child: Row(
                children: [
                  Icon(Icons.public, color: Color(0xFF0D47A1)),
                  SizedBox(width: 8),
                  Text('قنواتنا'),
                ],
              ),
            ),
            const PopupMenuItem<String>(
              value: 'contact',
              child: Row(
                children: [
                  Icon(Icons.mail, color: Color(0xFF0D47A1)),
                  SizedBox(width: 8),
                  Text('اتصل بنا'),
                ],
              ),
            ),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(
              Icons.arrow_forward,
              color: Colors.white,
              shadows: [
                Shadow(
                  offset: Offset(1.0, 1.0),
                  blurRadius: 3.0,
                  color: Color.fromARGB(150, 0, 0, 0),
                ),
              ],
            ),
            onPressed: () {
              // العودة إلى صفحة القائمة الرئيسية
              Navigator.pushReplacement(
                context,
                MaterialPageRoute(
                  builder: (context) => const MainMenuScreen(),
                ),
              );
            },
          ),
        ],
      ),
      // استخدام OrientationBuilder لإعادة بناء الواجهة عند تغيير اتجاه الشاشة
      body: Container(
        decoration: BoxDecoration(
          // استخدام صورة خلفية مختلفة حسب اتجاه الشاشة
          image: DecorationImage(
            image: AssetImage(
              MediaQuery.of(context).orientation == Orientation.portrait
                  ? 'assets/images/series_backgrounds/series_portrait_bg.jpg'
                  : 'assets/images/series_backgrounds/series_landscape_bg.jpg',
            ),
            fit: BoxFit.cover,
          ),
        ),
        child: Container(
          // طبقة شفافة فوق الخلفية لتحسين وضوح النص والأزرار
          decoration: const BoxDecoration(
            color: Color.fromRGBO(0, 0, 0, 0.4), // طبقة سوداء شفافة
          ),
          child: Padding(
            // إضافة مسافة إضافية في الأعلى لتعويض الشريط الشفاف
            padding: const EdgeInsets.only(top: 80.0),
            child: OrientationBuilder(
              builder: (context, orientation) {
                return Column(
                  children: [
                    // عنوان الصفحة
                    const Padding(
                      padding: EdgeInsets.all(16.0),
                      child: Text(
                        'اختر سلسلة للبدء',
                        style: TextStyle(
                          fontFamily: 'Cairo',
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                          shadows: [
                            Shadow(
                              offset: Offset(1.0, 1.0),
                              blurRadius: 3.0,
                              color: Color.fromARGB(150, 0, 0, 0),
                            ),
                          ],
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),

                    // قائمة السلاسل
                    Expanded(
                      child: Consumer<SeriesProvider>(
                        builder: (context, seriesProvider, child) {
                          if (seriesProvider.isLoading) {
                            return const Center(
                              child: CircularProgressIndicator(
                                  color: Color(0xFF2196F3)),
                            );
                          }

                          if (seriesProvider.series.isEmpty) {
                            return const Center(
                              child: Text(
                                'لا توجد سلاسل متاحة',
                                style: TextStyle(
                                  fontFamily: 'Cairo',
                                  fontSize: 18,
                                  color: Colors.white,
                                  shadows: [
                                    Shadow(
                                      offset: Offset(1.0, 1.0),
                                      blurRadius: 3.0,
                                      color: Color.fromARGB(150, 0, 0, 0),
                                    ),
                                  ],
                                ),
                              ),
                            );
                          }

                          // استخدام عدد مختلف من الأعمدة بناءً على اتجاه الشاشة
                          return GridView.builder(
                            padding: const EdgeInsets.all(16),
                            gridDelegate:
                                SliverGridDelegateWithFixedCrossAxisCount(
                              crossAxisCount: isLandscape(context)
                                  ? 3
                                  : 2, // 3 أعمدة في الوضع الأفقي، 2 في الوضع العمودي
                              childAspectRatio: isLandscape(context)
                                  ? 1.5
                                  : 1.2, // تعديل النسبة لتناسب العرض الجديد
                              crossAxisSpacing: 16,
                              mainAxisSpacing: 16,
                            ),
                            itemCount: seriesProvider.series.length,
                            itemBuilder: (context, index) {
                              final series = seriesProvider.series[index];
                              return _buildSeriesItem(series, context);
                            },
                          );
                        },
                      ),
                    ),
                  ],
                );
              },
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSeriesItem(Series series, BuildContext context) {
    return GestureDetector(
      onTap: () {
        // بدء الاختبار للسلسلة مباشرة
        final quizProvider = Provider.of<QuizProvider>(context, listen: false);
        quizProvider.startQuiz(series.id);
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const QuestionScreenNew()),
        );
      },
      child: Container(
        decoration: BoxDecoration(
          color: const Color(
              0xFF0D47A1), // لون أزرق غامق مطابق لخلفية القائمة الرئيسية
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(26),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Stack(
          children: [
            // محتوى الكرت
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // عنوان السلسلة
                  Expanded(
                    flex: 1,
                    child: LayoutBuilder(
                      builder: (context, constraints) {
                        // حساب حجم الخط بناءً على عرض البطاقة وحالة الشاشة
                        final isLandscape =
                            MediaQuery.of(context).orientation ==
                                Orientation.landscape;
                        final titleFontSize =
                            constraints.maxWidth * (isLandscape ? 0.12 : 0.15);
                        return Container(
                          alignment: Alignment.centerRight,
                          child: Text(
                            'السلسلة ${series.id}',
                            style: TextStyle(
                              fontFamily: 'Cairo',
                              fontSize: titleFontSize,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                            textAlign: TextAlign.right,
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        );
                      },
                    ),
                  ),
                  const SizedBox(height: 8),
                  // نتيجة آخر اختبار
                  Expanded(
                    flex: 1,
                    child: LayoutBuilder(
                      builder: (context, constraints) {
                        // حساب حجم الخط بناءً على عرض البطاقة وحالة الشاشة
                        final isLandscape =
                            MediaQuery.of(context).orientation ==
                                Orientation.landscape;
                        final scoreFontSize =
                            constraints.maxWidth * (isLandscape ? 0.14 : 0.18);
                        return Container(
                          alignment: Alignment.centerRight,
                          child: Text(
                            '${series.lastScore}/${series.totalQuestions}',
                            style: TextStyle(
                              fontFamily: 'Cairo',
                              fontSize: scoreFontSize,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                            textAlign: TextAlign.right,
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),

            // أيقونة التحميل في الزاوية العلوية اليسرى
            Positioned(
              top: 8,
              left: 8,
              child: _buildDownloadIcon(series, context),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDownloadIcon(Series series, BuildContext context) {
    if (series.isDownloading) {
      // مؤشر التحميل
      return const SizedBox(
        width: 24,
        height: 24,
        child: CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
        ),
      );
    } else if (series.isDownloaded) {
      // أيقونة تم التحميل (علامة صح خضراء) مع زر المراجعة لجميع السلاسل
      return Column(
        children: [
          // أيقونة الصح الخضراء
          GestureDetector(
            onLongPress: () {
              // عند الضغط المطول، نعرض خيار الحذف
              _handleDeleteSeries(series, context);
            },
            child: Container(
              width: 30,
              height: 30,
              decoration: const BoxDecoration(
                color: Colors.green,
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.check,
                color: Colors.white,
                size: 20,
              ),
            ),
          ),

          // زر المراجعة
          const SizedBox(height: 5),
          GestureDetector(
            onTap: () {
              _navigateToReviewScreen(series, context);
            },
            child: Container(
              width: 30,
              height: 30,
              decoration: const BoxDecoration(
                color: Colors.orange,
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.assessment,
                color: Colors.white,
                size: 20,
              ),
            ),
          ),
        ],
      );
    } else {
      // أيقونة التحميل
      return GestureDetector(
        onTap: () {
          _handleDownloadSeries(series, context);
        },
        child: Container(
          width: 30,
          height: 30,
          decoration: const BoxDecoration(
            color: Colors.green,
            shape: BoxShape.circle,
          ),
          child: const Icon(
            Icons.download,
            color: Colors.white,
            size: 20,
          ),
        ),
      );
    }
  }

  // دالة لمعالجة حذف السلسلة
  Future<void> _handleDeleteSeries(Series series, BuildContext context) async {
    // تأكيد الحذف
    final confirm = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف السلسلة', style: TextStyle(fontFamily: 'Cairo')),
        content: const Text('هل أنت متأكد من حذف هذه السلسلة؟',
            style: TextStyle(fontFamily: 'Cairo')),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء', style: TextStyle(fontFamily: 'Cairo')),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('حذف', style: TextStyle(fontFamily: 'Cairo')),
          ),
        ],
      ),
    );

    if (confirm != true || !context.mounted) return;

    // حفظ مرجع للمزود والمعرف
    final seriesProvider = Provider.of<SeriesProvider>(context, listen: false);
    final int seriesId = series.id;

    // حذف الملفات باستخدام SeriesDataServiceNew
    final seriesDataService = SeriesDataServiceNew();
    final success = await seriesDataService.deleteSeries(seriesId);

    // تحديث الحالة بعد الحذف
    if (!context.mounted) return;

    if (success) {
      seriesProvider.setDownloaded(seriesId, false);

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'تم حذف السلسلة $seriesId بنجاح',
            style: const TextStyle(fontFamily: 'Cairo'),
          ),
        ),
      );
    }
  }

  // دالة للانتقال إلى صفحة المراجعة
  void _navigateToReviewScreen(Series series, BuildContext context) {
    // الانتقال إلى صفحة المراجعة مع تمرير معرف السلسلة
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ReviewScreen(seriesId: series.id),
      ),
    );
  }

  // دالة لمعالجة تحميل السلسلة
  Future<void> _handleDownloadSeries(
      Series series, BuildContext context) async {
    if (!context.mounted) return;

    // التحقق من الاتصال بالإنترنت قبل التحميل
    final isConnected = await _connectivityService.checkConnectivity();
    if (!isConnected) {
      if (!context.mounted) return;

      // عرض رسالة عدم وجود اتصال بالإنترنت
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text(
            'لا يمكن تحميل السلسلة. يرجى التحقق من اتصال الإنترنت الخاص بك.',
            style: TextStyle(fontFamily: 'Cairo'),
          ),
          duration: Duration(seconds: 3),
        ),
      );
      return;
    }

    if (!context.mounted) return;

    // حفظ مرجع للمزود والمعرف
    final seriesProvider = Provider.of<SeriesProvider>(context, listen: false);
    final int seriesId = series.id;

    // بدء التحميل
    seriesProvider.setDownloading(seriesId, true);

    // محاولة تحميل الملفات من السحابة
    bool success = false;

    // استخدام SeriesDataServiceNew
    final seriesDataService = SeriesDataServiceNew();
    success = await seriesDataService.downloadSeries(seriesId);

    // التحقق من وجود الملفات بعد التحميل
    final filesExist = await seriesDataService.isSeriesDownloaded(seriesId);
    if (!filesExist) {
      // محاولة أخيرة للتحميل
      success = await seriesDataService.downloadSeries(seriesId);
    }

    // التحقق من اكتمال السلسلة بعد التحميل
    if (success) {
      // التحقق من اكتمال السلسلة (وجود جميع الصور والملفات الصوتية)
      final isComplete = await seriesDataService.isSeriesComplete(seriesId);

      // تحديث حالة اكتمال السلسلة
      await seriesProvider.setComplete(seriesId, isComplete);
    }

    // تحديث الحالة بعد التحميل
    if (!context.mounted) return;

    if (success) {
      seriesProvider.setDownloaded(seriesId, true);

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'تم تحميل السلسلة $seriesId بنجاح',
            style: const TextStyle(fontFamily: 'Cairo'),
          ),
        ),
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'فشل تحميل السلسلة $seriesId',
            style: const TextStyle(fontFamily: 'Cairo'),
          ),
        ),
      );
    }

    seriesProvider.setDownloading(seriesId, false);
  }
}
