$jsonFile = "assets/series/series_1/questions.json"
$jsonContent = Get-Content $jsonFile -Raw | ConvertFrom-Json

# Modify all questions to make explanation field empty
foreach ($question in $jsonContent.questions) {
    $question.explanation = ""
}

# Save the file with JSON formatting
$jsonOutput = $jsonContent | ConvertTo-Json -Depth 10
$jsonOutput | Set-Content $jsonFile -Encoding UTF8

Write-Host "Explanation fields have been emptied in series_1 questions.json file."
