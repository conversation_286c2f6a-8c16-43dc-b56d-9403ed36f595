import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/series.dart';
import '../models/question.dart';
import '../services/series_data_service_new.dart';

class SeriesProvider with ChangeNotifier {
  List<Series> _series = [];
  bool _isLoading = false;

  List<Series> get series => _series;
  bool get isLoading => _isLoading;

  SeriesProvider() {
    _loadSeries();
  }

  // دالة لإعادة تحميل السلاسل
  Future<void> refreshSeries() async {
    return _loadSeries();
  }

  Future<void> _loadSeries() async {
    _isLoading = true;
    notifyListeners();

    try {
      // استخدام الخدمة الجديدة لتحميل البيانات من المجلدات
      final seriesDataService = SeriesDataServiceNew();
      _series = await seriesDataService.getSeries();

      // الحصول على مثيل SharedPreferences
      final prefs = await SharedPreferences.getInstance();

      for (int i = 0; i < _series.length; i++) {
        final seriesId = _series[i].id;

        // تم إزالة المعالجة الخاصة للسلسلة 4

        // تحميل نتيجة آخر اختبار
        final lastScore = prefs.getInt('series_${seriesId}_lastScore') ?? 0;
        _series[i].lastScore = lastScore;

        // جعل جميع السلاسل محملة افتراضيًا
        _series[i].isDownloaded = true;
        await prefs.setBool('series_${seriesId}_downloaded', true);

        // تحميل حالة اكتمال السلسلة من التخزين المحلي
        bool isComplete = prefs.getBool('series_${seriesId}_complete') ?? false;

        // إذا لم تكن الحالة محفوظة، نتحقق من اكتمال السلسلة
        if (!isComplete) {
          // التحقق من اكتمال السلسلة (وجود جميع الصور والملفات الصوتية)
          isComplete = await seriesDataService.isSeriesComplete(seriesId);

          // حفظ حالة الاكتمال
          await prefs.setBool('series_${seriesId}_complete', isComplete);
        }

        // تعيين حالة اكتمال السلسلة
        _series[i].isComplete = isComplete;

        // جعل جميع السلاسل مكتملة
        _series[i].isComplete = true;
        await prefs.setBool('series_${seriesId}_complete', true);
      }
    } catch (e) {
      debugPrint('Error loading series: $e');
      // في حالة حدوث خطأ، نستخدم البيانات التجريبية
      _series = _createDummySeries();
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // تعيين حالة التحميل
  void setDownloading(int seriesId, bool isDownloading) {
    final index = _series.indexWhere((s) => s.id == seriesId);
    if (index != -1) {
      _series[index].isDownloading = isDownloading;
      notifyListeners();
    }
  }

  // تعيين حالة الاكتمال
  Future<void> setDownloaded(int seriesId, bool isDownloaded) async {
    final index = _series.indexWhere((s) => s.id == seriesId);
    if (index != -1) {
      _series[index].isDownloaded = isDownloaded;

      // حفظ الحالة
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('series_${seriesId}_downloaded', isDownloaded);

      notifyListeners();
    }
  }

  // تعيين حالة اكتمال السلسلة
  Future<void> setComplete(int seriesId, bool isComplete) async {
    final index = _series.indexWhere((s) => s.id == seriesId);
    if (index != -1) {
      _series[index].isComplete = isComplete;

      // حفظ الحالة
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('series_${seriesId}_complete', isComplete);

      notifyListeners();
    }
  }

  // تحديث نتيجة آخر اختبار
  Future<void> updateLastScore(int seriesId, int score) async {
    final index = _series.indexWhere((s) => s.id == seriesId);
    if (index != -1) {
      _series[index].lastScore = score;

      // حفظ النتيجة
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt('series_${seriesId}_lastScore', score);

      notifyListeners();
    }
  }

  // الحصول على سلسلة معينة
  Series? getSeriesById(int id) {
    try {
      return _series.firstWhere((series) => series.id == id);
    } catch (e) {
      return null;
    }
  }

  // إنشاء بيانات تجريبية
  List<Series> _createDummySeries() {
    return [
      Series(
        id: 1,
        title: 'السلسلة الأولى',
        description: 'تتضمن أسئلة عن قواعد المرور الأساسية',
        questions: [
          Question(
            id: 1,
            imageUrl: 'https://i.imgur.com/z8xHyT8.jpg',
            audioUrl: 'assets/audio/question_1_1.mp3',
            questionText: 'نسبة الراجلين من قتلى حوادث السير بالمغرب :',
            answers: [
              Answer(id: 1, text: '10%'),
              Answer(id: 2, text: '20%'),
              Answer(id: 3, text: 'فوق 25%'),
            ],
            correctAnswerIndices: [2],
            explanation:
                'تشكل نسبة الراجلين من قتلى حوادث السير بالمغرب أكثر من 25% من مجموع الضحايا.',
          ),
          Question(
            id: 2,
            imageUrl: 'https://i.imgur.com/XqwRRVt.jpg',
            audioUrl: 'assets/audio/question_1_2.mp3',
            questionText:
                'ما بين هاد العلامات : نقدر نحط الرايكلي معها : نعم .......... 1 لا .......... 2 - نقدر نتوقف بفرة : نعم .......... 3 لا .......... 4',
            answers: [
              Answer(id: 1, text: 'نعم'),
              Answer(id: 2, text: 'لا'),
              Answer(id: 3, text: 'نعم'),
              Answer(id: 4, text: 'لا'),
            ],
            correctAnswerIndices: [1, 3],
            explanation:
                'لا يمكن وضع الدراجة الهوائية في هذه المنطقة ولا يمكن التوقف بفترة.',
          ),
        ],
      ),
      Series(
        id: 2,
        title: 'السلسلة الثانية',
        description: 'تتضمن أسئلة عن إشارات المرور',
        questions: [
          Question(
            id: 1,
            imageUrl: 'https://i.imgur.com/z8xHyT8.jpg',
            audioUrl: 'assets/audio/question_2_1.mp3',
            questionText: 'نسبة الراجلين من قتلى حوادث السير بالمغرب :',
            answers: [
              Answer(id: 1, text: '10%'),
              Answer(id: 2, text: '20%'),
              Answer(id: 3, text: 'فوق 25%'),
            ],
            correctAnswerIndices: [2],
            explanation:
                'تشكل نسبة الراجلين من قتلى حوادث السير بالمغرب أكثر من 25% من مجموع الضحايا.',
          ),
        ],
      ),
      Series(
        id: 3,
        title: 'السلسلة الثالثة',
        description: 'تتضمن أسئلة عن السلامة المرورية',
        questions: [
          Question(
            id: 1,
            imageUrl: 'https://i.imgur.com/XqwRRVt.jpg',
            audioUrl: 'assets/audio/question_3_1.mp3',
            questionText:
                'ما بين هاد العلامات : نقدر نحط الرايكلي معها : نعم .......... 1 لا .......... 2 - نقدر نتوقف بفرة : نعم .......... 3 لا .......... 4',
            answers: [
              Answer(id: 1, text: 'نعم'),
              Answer(id: 2, text: 'لا'),
              Answer(id: 3, text: 'نعم'),
              Answer(id: 4, text: 'لا'),
            ],
            correctAnswerIndices: [1, 3],
            explanation:
                'لا يمكن وضع الدراجة الهوائية في هذه المنطقة ولا يمكن التوقف بفترة.',
          ),
        ],
      ),
    ];
  }
}
