import 'dart:io';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';

class SimpleCopyService {
  // إنشاء ملفات السلسلة بشكل مباشر
  static Future<bool> createSeriesFiles(int seriesId) async {
    try {
      debugPrint('بدء إنشاء ملفات السلسلة $seriesId');

      // الحصول على مسار التخزين المحلي
      final appDir = await getApplicationDocumentsDirectory();
      final seriesDir = '${appDir.path}/series_$seriesId';

      debugPrint('مسار التخزين المحلي: $seriesDir');

      // إنشاء المجلدات
      final seriesDirObj = Directory(seriesDir);
      if (!await seriesDirObj.exists()) {
        await seriesDirObj.create(recursive: true);
        debugPrint('تم إنشاء مجلد السلسلة: $seriesDir');
      }

      final imagesDir = Directory('$seriesDir/images');
      if (!await imagesDir.exists()) {
        await imagesDir.create(recursive: true);
        debugPrint('تم إنشاء مجلد الصور: ${imagesDir.path}');
      }

      final audioDir = Directory('$seriesDir/audio');
      if (!await audioDir.exists()) {
        await audioDir.create(recursive: true);
        debugPrint('تم إنشاء مجلد الصوتيات: ${audioDir.path}');
      }

      // إنشاء ملف الإجابات الصحيحة للأسئلة الـ 40
      final answersFile = File('$seriesDir/correct_answers.json');
      if (!await answersFile.exists()) {
        // إنشاء إجابات افتراضية لـ 40 سؤال
        final Map<String, List<int>> answers = {};
        for (int i = 1; i <= 40; i++) {
          answers['$i'] = [0]; // الإجابة الافتراضية هي الخيار الأول
        }
        await answersFile.writeAsString(
            '{${answers.entries.map((e) => '"${e.key}":${e.value}').join(',')}}');
        debugPrint('تم إنشاء ملف الإجابات الصحيحة: ${answersFile.path}');
      }

      // إنشاء ملف صورة افتراضي للسؤال الأول
      final imageFile = File('$seriesDir/images/series_1_image_1.jpg');
      if (!await imageFile.exists()) {
        await imageFile.writeAsString('محتوى افتراضي للصورة');
        debugPrint('تم إنشاء ملف صورة افتراضي: ${imageFile.path}');
      }

      // إنشاء ملف صوتي افتراضي للسؤال الأول
      final audioFile = File('$seriesDir/audio/series_1_audio_1.mp3');
      if (!await audioFile.exists()) {
        await audioFile.writeAsString('محتوى افتراضي للملف الصوتي');
        debugPrint('تم إنشاء ملف صوتي افتراضي: ${audioFile.path}');
      }

      // محاولة نسخ الملفات من المجلد الحالي إذا كانت موجودة
      try {
        final currentDir = Directory.current.path;
        debugPrint('المجلد الحالي: $currentDir');

        // محاولة نسخ الصورة
        final externalImagePath =
            '$currentDir/assets/images/series_1_image_1.jpg';
        final externalImageFile = File(externalImagePath);
        if (await externalImageFile.exists()) {
          await externalImageFile.copy(imageFile.path);
          debugPrint(
              'تم نسخ الصورة من: $externalImagePath إلى: ${imageFile.path}');
        }

        // محاولة نسخ الملف الصوتي
        final externalAudioPath =
            '$currentDir/assets/audio/series_1_audio_1.mp3';
        final externalAudioFile = File(externalAudioPath);
        if (await externalAudioFile.exists()) {
          await externalAudioFile.copy(audioFile.path);
          debugPrint(
              'تم نسخ الملف الصوتي من: $externalAudioPath إلى: ${audioFile.path}');
        }
      } catch (e) {
        debugPrint('خطأ في نسخ الملفات من المجلد الحالي: $e');
        // لا نريد إيقاف العملية إذا فشلت محاولة النسخ
      }

      debugPrint('تم إنشاء ملفات السلسلة $seriesId بنجاح');
      return true;
    } catch (e) {
      debugPrint('خطأ في إنشاء ملفات السلسلة: $e');
      return false;
    }
  }

  // التحقق من وجود ملفات السلسلة
  static Future<bool> checkSeriesFiles(int seriesId) async {
    try {
      final appDir = await getApplicationDocumentsDirectory();
      final seriesDir = '${appDir.path}/series_$seriesId';

      // التحقق من وجود المجلدات
      final seriesDirObj = Directory(seriesDir);
      if (!await seriesDirObj.exists()) {
        debugPrint('مجلد السلسلة غير موجود: $seriesDir');
        return false;
      }

      final imagesDir = Directory('$seriesDir/images');
      if (!await imagesDir.exists()) {
        debugPrint('مجلد الصور غير موجود: ${imagesDir.path}');
        return false;
      }

      final audioDir = Directory('$seriesDir/audio');
      if (!await audioDir.exists()) {
        debugPrint('مجلد الصوتيات غير موجود: ${audioDir.path}');
        return false;
      }

      // التحقق من وجود ملف الإجابات الصحيحة
      final answersFile = File('$seriesDir/correct_answers.json');
      if (!await answersFile.exists()) {
        debugPrint('ملف الإجابات الصحيحة غير موجود: ${answersFile.path}');
        return false;
      }

      // التحقق من وجود ملف صورة واحد على الأقل
      final imageFile = File('$seriesDir/images/series_1_image_1.jpg');
      if (!await imageFile.exists()) {
        debugPrint('ملف الصورة غير موجود: ${imageFile.path}');
        return false;
      }

      // التحقق من وجود ملف صوتي واحد على الأقل
      final audioFile = File('$seriesDir/audio/series_1_audio_1.mp3');
      if (!await audioFile.exists()) {
        debugPrint('ملف الصوت غير موجود: ${audioFile.path}');
        return false;
      }

      debugPrint('تم التحقق من وجود ملفات السلسلة $seriesId بنجاح');
      return true;
    } catch (e) {
      debugPrint('خطأ في التحقق من وجود ملفات السلسلة: $e');
      return false;
    }
  }

  // حذف ملفات السلسلة
  static Future<bool> deleteSeriesFiles(int seriesId) async {
    try {
      final appDir = await getApplicationDocumentsDirectory();
      final seriesDir = '${appDir.path}/series_$seriesId';

      final seriesDirObj = Directory(seriesDir);
      if (await seriesDirObj.exists()) {
        await seriesDirObj.delete(recursive: true);
        debugPrint('تم حذف مجلد السلسلة: $seriesDir');
        return true;
      }

      return false;
    } catch (e) {
      debugPrint('خطأ في حذف ملفات السلسلة: $e');
      return false;
    }
  }
}
