import 'package:flutter/material.dart';
import 'main_menu_screen.dart';
import '../services/connectivity_service.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({Key? key}) : super(key: key);

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final ConnectivityService _connectivityService = ConnectivityService();

  @override
  void initState() {
    super.initState();
    // تأخير التحقق من الاتصال حتى يتم بناء الواجهة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _connectivityService.showInitialConnectionDialog(context);
      _connectivityService.startMonitoring(context);
    });
  }

  @override
  void dispose() {
    _connectivityService.stopMonitoring();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      body: Container(
        decoration: BoxDecoration(
          // استخدام صورة خلفية مختلفة حسب اتجاه الشاشة
          image: DecorationImage(
            image: AssetImage(
              MediaQuery.of(context).orientation == Orientation.portrait
                  ? 'assets/images/backgrounds/portrait_bg.jpg'
                  : 'assets/images/backgrounds/landscape_bg.jpg',
            ),
            fit: BoxFit.cover,
          ),
        ),
        child: Container(
          // طبقة شفافة فوق الخلفية لتحسين وضوح النص والأزرار
          decoration: const BoxDecoration(
            color: Color.fromRGBO(0, 0, 0, 0.4), // طبقة سوداء شفافة
          ),
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // شعار التطبيق
                Image.asset(
                  'assets/images/logo.png',
                  width: 180, // تصغير حجم الشعار
                  fit: BoxFit.contain,
                ),
                const SizedBox(height: 10),
                const Text(
                  'نجاحك الآن بين يديك',
                  style: TextStyle(
                    fontFamily: 'Cairo',
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                    shadows: [
                      Shadow(
                        offset: Offset(1.0, 1.0),
                        blurRadius: 3.0,
                        color: Color.fromARGB(150, 0, 0, 0),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 50),
                // زر البدء
                ElevatedButton(
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                          builder: (context) => const MainMenuScreen()),
                    );
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                        horizontal: 50, vertical: 15),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(30),
                    ),
                  ),
                  child: const Text(
                    'ابدأ',
                    style: TextStyle(
                      fontFamily: 'Cairo',
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
