# Script to create series 6 to 10

$seriesTitles = @(
    "السلسلة السادسة",
    "السلسلة السابعة",
    "السلسلة الثامنة",
    "السلسلة التاسعة",
    "السلسلة العاشرة"
)

$seriesDescriptions = @(
    "تتضمن أسئلة عن الوقوف والتوقف",
    "تتضمن أسئلة عن القيادة الليلية",
    "تتضمن أسئلة عن القيادة في الظروف الجوية المختلفة",
    "تتضمن أسئلة عن صيانة المركبة",
    "تتضمن أسئلة عن الإسعافات الأولية"
)

for ($i = 6; $i -le 10; $i++) {
    $seriesFolder = "assets\series\series_$i"
    $audioFolder = "$seriesFolder\audio"
    $imagesFolder = "$seriesFolder\images"
    $questionsFolder = "$seriesFolder\questions"
    
    # Create folders
    New-Item -ItemType Directory -Path $seriesFolder -Force
    New-Item -ItemType Directory -Path $audioFolder -Force
    New-Item -ItemType Directory -Path $imagesFolder -Force
    New-Item -ItemType Directory -Path $questionsFolder -Force
    
    # Create data.json
    $dataJson = @"
{
  "id": $i,
  "title": "$($seriesTitles[$i-6])",
  "description": "$($seriesDescriptions[$i-6])",
  "questionsCount": 40
}
"@
    Set-Content -Path "$seriesFolder\data.json" -Value $dataJson
    
    # Create questions.json with 2 sample questions
    $questionsJson = @"
{
  "questions": [
    {
      "id": 1,
      "imageUrl": "assets/series/series_$i/images/image_1.jpg",
      "audioUrl": "assets/series/series_$i/audio/series_${i}_audio_1.mp3",
      "questionText": "سؤال 1",
      "answers": [
        {"id": 1, "text": "إجابة 1"},
        {"id": 2, "text": "إجابة 2"},
        {"id": 3, "text": "إجابة 3"}
      ],
      "correctAnswerIndices": [2],
      "explanation": "شرح الإجابة الصحيحة للسؤال 1."
    },
    {
      "id": 2,
      "imageUrl": "assets/series/series_$i/images/image_2.jpg",
      "audioUrl": "assets/series/series_$i/audio/series_${i}_audio_2.mp3",
      "questionText": "سؤال 2",
      "answers": [
        {"id": 1, "text": "إجابة 1"},
        {"id": 2, "text": "إجابة 2"},
        {"id": 3, "text": "إجابة 3"}
      ],
      "correctAnswerIndices": [1],
      "explanation": "شرح الإجابة الصحيحة للسؤال 2."
    }
  ]
}
"@
    Set-Content -Path "$seriesFolder\questions.json" -Value $questionsJson
    
    # Create placeholder audio and image files
    Set-Content -Path "$audioFolder\series_${i}_audio_1.mp3" -Value "placeholder_audio"
    Set-Content -Path "$imagesFolder\image_1.txt" -Value "placeholder_image"
    
    Write-Host "Created series $i: $($seriesTitles[$i-6])"
}

Write-Host "All series created successfully!"
