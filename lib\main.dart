import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:provider/provider.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'providers/settings_provider.dart';
import 'providers/quiz_provider.dart';
import 'providers/series_provider.dart';
import 'providers/lessons_quiz_provider.dart';
import 'services/connectivity_service.dart';
import 'screens/home_screen.dart';
import 'screens/series_screen_new.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // تهيئة خدمة الإعلانات فقط إذا لم يكن التطبيق يعمل على الويب
  if (!kIsWeb) {
    try {
      await MobileAds.instance.initialize();

      // إضافة جهاز الاختبار
      MobileAds.instance.updateRequestConfiguration(
        RequestConfiguration(
          testDeviceIds: [
            '3559e7c4-1868-43fc-9b5c-ea1df8174944' // استبدل هذا بمعرف جهازك الحقيقي
          ],
        ),
      );

      debugPrint('تم تهيئة خدمة الإعلانات بنجاح');
    } catch (e) {
      debugPrint('فشل في تهيئة خدمة الإعلانات: $e');
    }
  } else {
    debugPrint('تم تخطي تهيئة الإعلانات لأن التطبيق يعمل على الويب');
  }

  // تشغيل التطبيق
  runApp(const MyApp());
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  final ConnectivityService _connectivityService = ConnectivityService();

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    _connectivityService.stopMonitoring();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => SettingsProvider()),
        ChangeNotifierProvider(create: (_) => QuizProvider()),
        ChangeNotifierProvider(create: (_) => SeriesProvider()),
        ChangeNotifierProvider(create: (_) => LessonsQuizProvider()),
      ],
      child: MaterialApp(
        title: 'سياقة',
        debugShowCheckedModeBanner: false,
        theme: ThemeData(
          primarySwatch: Colors.blue,
          fontFamily: 'Cairo',
          appBarTheme: const AppBarTheme(
            centerTitle: true,
            elevation: 0,
          ),
          scaffoldBackgroundColor: Colors.white,
        ),
        locale: const Locale('ar', 'MA'),
        supportedLocales: const [
          Locale('ar', 'MA'),
        ],
        localizationsDelegates: const [
          GlobalMaterialLocalizations.delegate,
          GlobalWidgetsLocalizations.delegate,
          GlobalCupertinoLocalizations.delegate,
        ],
        initialRoute: '/',
        routes: {
          '/': (context) => Builder(
                builder: (context) {
                  // تأخير التحقق من الاتصال حتى يتم بناء السياق
                  WidgetsBinding.instance.addPostFrameCallback((_) {
                    _connectivityService.showInitialConnectionDialog(context);
                    _connectivityService.startMonitoring(context);
                  });
                  return const HomeScreen();
                },
              ),
          '/series': (context) => const SeriesScreenNew(),
        },
      ),
    );
  }
}
