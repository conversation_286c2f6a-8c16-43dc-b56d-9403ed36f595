import 'question.dart';

class Series {
  final int id;
  final String title;
  final List<Question> questions;
  final String description;
  bool isDownloaded;
  bool isDownloading;
  bool isComplete; // إضافة خاصية جديدة لتحديد ما إذا كانت السلسلة مكتملة أم لا
  int lastScore; // نتيجة آخر اختبار
  int totalQuestions; // إجمالي عدد الأسئلة

  Series({
    required this.id,
    required this.title,
    required this.questions,
    this.description = '',
    this.isDownloaded = false,
    this.isDownloading = false,
    this.isComplete = true, // افتراضياً، السلسلة مكتملة
    this.lastScore = 0,
    this.totalQuestions = 40,
  });

  factory Series.fromJson(Map<String, dynamic> json) {
    return Series(
      id: json['id'],
      title: json['title'],
      description: json['description'] ?? '',
      questions: (json['questions'] as List)
          .map((question) => Question.fromJson(question))
          .toList(),
      isDownloaded: json['isDownloaded'] ?? false,
      isDownloading: json['isDownloading'] ?? false,
      isComplete: json['isComplete'] ?? true, // إضافة الخاصية الجديدة
      lastScore: json['lastScore'] ?? 0,
      totalQuestions: json['totalQuestions'] ?? 40,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'questions': questions.map((question) => question.toJson()).toList(),
      'isDownloaded': isDownloaded,
      'isDownloading': isDownloading,
      'isComplete': isComplete, // إضافة الخاصية الجديدة
      'lastScore': lastScore,
      'totalQuestions': totalQuestions,
    };
  }

  Series copyWith({
    int? id,
    String? title,
    List<Question>? questions,
    String? description,
    bool? isDownloaded,
    bool? isDownloading,
    bool? isComplete, // إضافة الخاصية الجديدة
    int? lastScore,
    int? totalQuestions,
  }) {
    return Series(
      id: id ?? this.id,
      title: title ?? this.title,
      questions: questions ?? this.questions,
      description: description ?? this.description,
      isDownloaded: isDownloaded ?? this.isDownloaded,
      isDownloading: isDownloading ?? this.isDownloading,
      isComplete: isComplete ?? this.isComplete, // إضافة الخاصية الجديدة
      lastScore: lastScore ?? this.lastScore,
      totalQuestions: totalQuestions ?? this.totalQuestions,
    );
  }
}
