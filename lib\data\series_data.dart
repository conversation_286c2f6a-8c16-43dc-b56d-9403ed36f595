import '../models/series.dart';
import '../models/question.dart';

class SeriesData {
  // الحصول على قائمة السلاسل
  static List<Series> getSeries() {
    return [
      Series(
        id: 1,
        title: 'السلسلة 1',
        description: 'سلسلة أسئلة متنوعة',
        questions: getSeriesQuestions(1),
        isDownloaded: true, // جعل السلسلة محملة افتراضيًا
        isDownloading: false,
        totalQuestions: 40,
      ),
      Series(
        id: 2,
        title: 'السلسلة 2',
        description: 'سلسلة أسئلة متنوعة',
        questions: getSeriesQuestions(2),
        isDownloaded: true, // جعل السلسلة محملة افتراضيًا
        isDownloading: false,
        totalQuestions: 40,
      ),
      Series(
        id: 3,
        title: 'السلسلة 3',
        description: 'سلسلة أسئلة متنوعة',
        questions: getSeriesQuestions(3),
        isDownloaded: true, // جعل السلسلة محملة افتراضيًا
        isDownloading: false,
        totalQuestions: 40,
      ),
    ];
  }

  // الحصول على أسئلة السلسلة
  static List<Question> getSeriesQuestions(int seriesId) {
    // إنشاء 40 سؤال للسلسلة
    List<Question> questions = [];

    for (int i = 1; i <= 40; i++) {
      questions.add(
        Question(
          id: i,
          imageUrl: 'https://i.imgur.com/z8xHyT8.jpg',
          audioUrl: 'assets/audio/series_1_audio_1.mp3',
          questionText: 'السؤال رقم $i في السلسلة $seriesId',
          answers: [
            Answer(id: 1, text: 'الخيار الأول'),
            Answer(id: 2, text: 'الخيار الثاني'),
            Answer(id: 3, text: 'الخيار الثالث'),
            Answer(id: 4, text: 'الخيار الرابع'),
          ],
          correctAnswerIndices: [0], // الإجابة الصحيحة هي الخيار الأول
          explanation: 'شرح السؤال رقم $i في السلسلة $seriesId',
        ),
      );
    }

    return questions;
  }

  // الحصول على الإجابات الصحيحة للسلسلة
  static Map<int, List<int>> getSeriesCorrectAnswers(int seriesId) {
    // إنشاء إجابات افتراضية لـ 40 سؤال
    Map<int, List<int>> answers = {};

    for (int i = 1; i <= 40; i++) {
      answers[i] = [0]; // الإجابة الافتراضية هي الخيار الأول
    }

    return answers;
  }
}
