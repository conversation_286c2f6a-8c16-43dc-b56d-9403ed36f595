import 'package:flutter/material.dart';
import 'dart:convert';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/series.dart';
import '../models/question.dart';
import '../providers/lessons_quiz_provider.dart';
import 'settings_screen.dart';
import 'channels_screen.dart';
import 'contact_screen.dart';
import 'lessons_question_screen.dart';
import 'lessons_review_screen.dart';
import 'package:siya9a/services/image_downloader.dart';

class LessonsSeriesScreen extends StatefulWidget {
  const LessonsSeriesScreen({Key? key}) : super(key: key);

  @override
  State<LessonsSeriesScreen> createState() => _LessonsSeriesScreenState();
}

class _LessonsSeriesScreenState extends State<LessonsSeriesScreen> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  // دالة للتحقق من اتجاه الشاشة (أفقي أو عمودي)
  bool isLandscape(BuildContext context) {
    return MediaQuery.of(context).orientation == Orientation.landscape;
  }

  // قائمة سلاسل الدروس
  List<Series> _lessonsSeries = [];

  @override
  void initState() {
    super.initState();
    _loadLessonsSeries();
  }

  // تحميل سلاسل الدروس من ملف index.json
  Future<void> _loadLessonsSeries() async {
    try {
      // قراءة ملف index.json
      String jsonString = await DefaultAssetBundle.of(context)
          .loadString('assets/lessons_series/index.json');
      Map<String, dynamic> jsonData = json.decode(jsonString);

      // تحويل البيانات إلى قائمة من السلاسل
      List<dynamic> seriesData = jsonData['series'];
      List<Series> series = [];

      for (var data in seriesData) {
        // إنشاء كائن السلسلة
        Series newSeries = Series(
          id: data['id'],
          title: data['title'],
          description: data['description'],
          questions: [],
          isDownloaded: true,
          isComplete: true,
          totalQuestions: data['questionsCount'],
        );

        // تحميل نتيجة آخر اختبار من التخزين المحلي
        await _loadLastScore(newSeries);

        series.add(newSeries);
      }

      setState(() {
        _lessonsSeries = series;
      });
    } catch (e) {
      debugPrint('Error loading lessons series: $e');
    }
  }

  // دالة لتحميل نتيجة آخر اختبار من التخزين المحلي
  Future<void> _loadLastScore(Series series) async {
    try {
      // الحصول على مثيل من SharedPreferences
      final prefs = await SharedPreferences.getInstance();

      // استعادة نتيجة آخر اختبار
      final lastScore = prefs.getInt('lessons_last_score_${series.id}');
      if (lastScore != null) {
        series.lastScore = lastScore;
        debugPrint(
            'تم تحميل نتيجة آخر اختبار للسلسلة ${series.id}: $lastScore');
      } else {
        debugPrint('لا توجد نتيجة محفوظة للسلسلة ${series.id}');
      }
    } catch (e) {
      debugPrint('خطأ في تحميل نتيجة آخر اختبار: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: _scaffoldKey,
      extendBodyBehindAppBar: true, // تمديد الخلفية خلف شريط التطبيق
      appBar: AppBar(
        title: const Text(
          'سلاسل الدروس',
          style: TextStyle(
            fontFamily: 'Cairo',
            fontWeight: FontWeight.bold,
            color: Colors.white,
            shadows: [
              Shadow(
                offset: Offset(1.0, 1.0),
                blurRadius: 3.0,
                color: Color.fromARGB(150, 0, 0, 0),
              ),
            ],
          ),
        ),
        backgroundColor: Colors.transparent, // جعل الشريط شفافًا
        elevation: 0, // إزالة الظل
        centerTitle: true,
        // إضافة تأثير زجاجي للشريط
        flexibleSpace: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Color.fromRGBO(0, 0, 0, 0.4),
                Color.fromRGBO(0, 0, 0, 0.1),
              ],
            ),
          ),
        ),
        // إزالة leading وإضافة زر العودة في actions على اليمين
        automaticallyImplyLeading: false, // إلغاء زر العودة التلقائي
        actions: [
          IconButton(
            icon: const Icon(
              Icons.arrow_forward,
              color: Colors.white,
              shadows: [
                Shadow(
                  offset: Offset(1.0, 1.0),
                  blurRadius: 3.0,
                  color: Color.fromARGB(150, 0, 0, 0),
                ),
              ],
            ), // سهم للأمام (يمين) للعودة
            onPressed: () {
              Navigator.pop(context);
            },
          ),
        ],
        // إضافة قائمة منسدلة
        leading: PopupMenuButton<String>(
          icon: const Icon(
            Icons.menu,
            color: Colors.white,
            shadows: [
              Shadow(
                offset: Offset(1.0, 1.0),
                blurRadius: 3.0,
                color: Color.fromARGB(150, 0, 0, 0),
              ),
            ],
          ),
          onSelected: (value) {
            if (value == 'settings') {
              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => const SettingsScreen()),
              );
            } else if (value == 'channels') {
              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => const ChannelsScreen()),
              );
            } else if (value == 'contact') {
              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => const ContactScreen()),
              );
            }
          },
          itemBuilder: (BuildContext context) => <PopupMenuEntry<String>>[
            const PopupMenuItem<String>(
              value: 'settings',
              child: Row(
                children: [
                  Icon(Icons.settings, color: Color(0xFF0D47A1)),
                  SizedBox(width: 8),
                  Text('الإعدادات'),
                ],
              ),
            ),
            const PopupMenuItem<String>(
              value: 'channels',
              child: Row(
                children: [
                  Icon(Icons.public, color: Color(0xFF0D47A1)),
                  SizedBox(width: 8),
                  Text('قنواتنا'),
                ],
              ),
            ),
            const PopupMenuItem<String>(
              value: 'contact',
              child: Row(
                children: [
                  Icon(Icons.mail, color: Color(0xFF0D47A1)),
                  SizedBox(width: 8),
                  Text('اتصل بنا'),
                ],
              ),
            ),
          ],
        ),
      ),
      body: Container(
        decoration: BoxDecoration(
          // استخدام صورة خلفية مختلفة حسب اتجاه الشاشة
          image: DecorationImage(
            image: AssetImage(
              MediaQuery.of(context).orientation == Orientation.portrait
                  ? 'assets/images/series_backgrounds/series_portrait_bg.jpg'
                  : 'assets/images/series_backgrounds/series_landscape_bg.jpg',
            ),
            fit: BoxFit.cover,
          ),
        ),
        child: Container(
          // طبقة شفافة فوق الخلفية لتحسين وضوح النص والأزرار
          decoration: const BoxDecoration(
            color: Color.fromRGBO(0, 0, 0, 0.4), // طبقة سوداء شفافة
          ),
          child: SafeArea(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  // عنوان الصفحة
                  const Padding(
                    padding: EdgeInsets.only(bottom: 16.0),
                    child: Text(
                      'اختر سلسلة',
                      style: TextStyle(
                        fontFamily: 'Cairo',
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                        shadows: [
                          Shadow(
                            offset: Offset(1.0, 1.0),
                            blurRadius: 3.0,
                            color: Color.fromARGB(150, 0, 0, 0),
                          ),
                        ],
                      ),
                    ),
                  ),

                  // قائمة سلاسل الدروس
                  Expanded(
                    child: GridView.builder(
                      padding: const EdgeInsets.all(8),
                      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: isLandscape(context) ? 3 : 2,
                        childAspectRatio: isLandscape(context) ? 1.5 : 1.2,
                        crossAxisSpacing: 16,
                        mainAxisSpacing: 16,
                      ),
                      itemCount: _lessonsSeries.length,
                      itemBuilder: (context, index) {
                        final series = _lessonsSeries[index];
                        return _buildSeriesItem(series, context);
                      },
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

// بناء عنصر سلسلة الدروس
  Widget _buildSeriesItem(Series series, BuildContext context) {
    return GestureDetector(
      onTap: () async {
        double progress = 0.0;
        late StateSetter setDialogState;

        try {
          // ✅ عرض نافذة انتظار التحميل مع نسبة مئوية
          showDialog(
            context: context,
            barrierDismissible: false,
            builder: (context) {
              return StatefulBuilder(
                builder: (context, setState) {
                  setDialogState = setState;
                  return PopScope(
                    canPop: false, // منع الرجوع
                    child: AlertDialog(
                      title: const Text("تحميل السلسلة..."),
                      content: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          LinearProgressIndicator(
                            value: progress,
                            color: const Color.fromARGB(255, 13, 61, 163),
                          ),
                          const SizedBox(height: 16),
                          Text(
                            "تم تحميل ${(progress * 100).toInt()}%",
                            style: const TextStyle(fontSize: 16),
                          ),
                        ],
                      ),
                    ),
                  );
                },
              );
            },
          );

          // تحميل أسئلة السلسلة
          String jsonString = await DefaultAssetBundle.of(context).loadString(
              'assets/lessons_series/series_${series.id}/questions.json');
          Map<String, dynamic> jsonData = json.decode(jsonString);

          List<dynamic> questionsData = jsonData['questions'];
          List<Question> questions = [];

          // استخراج روابط الصور
          List<String> imageUrls = [];

          for (var data in questionsData) {
            List<Answer> answers = [];
            for (var answer in data['answers']) {
              answers.add(Answer(
                id: answer['id'],
                text: answer['text'],
              ));
            }

            questions.add(Question(
              id: data['id'],
              imageUrl: data['imageUrl'],
              audioUrl: data['audioUrl'],
              questionText: data['questionText'],
              answers: answers,
              correctAnswerIndices:
                  List<int>.from(data['correctAnswerIndices']),
              explanation: data['explanation'],
            ));

            imageUrls.add(data['imageUrl']);
          }

          // تحميل الصور وتحديث التقدم
          List<String> localImagePaths = [];
          for (int i = 0; i < imageUrls.length; i++) {
            try {
              final path =
                  await downloadImage(imageUrls[i], "series_${series.id}");
              localImagePaths.add(path);
            } catch (e) {
              debugPrint("خطأ في تحميل صورة: $e");
            }

            // ✅ تحديث النسبة في الواجهة
            progress = (i + 1) / imageUrls.length;
            setDialogState(() {});
          }

          // ❗️ربط الصور المحملة محليًا مع الأسئلة
          for (int i = 0; i < questions.length; i++) {
            if (i < localImagePaths.length) {
              questions[i] =
                  questions[i].copyWith(imageUrl: localImagePaths[i]);
            }
          }

          // إغلاق نافذة التحميل بعد انتهاء العمل
          if (!context.mounted) return;
          Navigator.of(context).pop();

          if (!context.mounted) return;

          Series seriesWithQuestions = Series(
            id: series.id,
            title: series.title,
            description: series.description,
            questions: questions,
            isDownloaded: true,
            isComplete: true,
            totalQuestions: series.totalQuestions,
          );

          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => ChangeNotifierProvider(
                create: (context) => LessonsQuizProvider(),
                child: LessonsQuestionScreen(series: seriesWithQuestions),
              ),
            ),
          ).then((_) {
            _loadLessonsSeries();
          });
        } catch (e) {
          if (!context.mounted) return;
          Navigator.of(context).pop(); // إغلاق نافذة التحميل حتى في حالة الخطأ
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'حدث خطأ أثناء تحميل السلسلة: $e',
                style: const TextStyle(fontFamily: 'Cairo'),
              ),
              duration: const Duration(seconds: 3),
            ),
          );
        }
      },
      child: Container(
        decoration: BoxDecoration(
          color: const Color(0xFF0D47A1), // لون أزرق غامق
          borderRadius: BorderRadius.circular(12),
          boxShadow: const [
            BoxShadow(
              color: Color.fromRGBO(0, 0, 0, 0.1),
              blurRadius: 4,
              offset: Offset(0, 2),
            ),
          ],
        ),
        child: Stack(
          children: [
            // محتوى البطاقة
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // عنوان السلسلة
                  Expanded(
                    flex: 3,
                    child: LayoutBuilder(
                      builder: (context, constraints) {
                        // حساب حجم الخط بناءً على عرض البطاقة وحالة الشاشة
                        final isLandscape =
                            MediaQuery.of(context).orientation ==
                                Orientation.landscape;
                        final titleFontSize =
                            constraints.maxWidth * (isLandscape ? 0.12 : 0.15);
                        return Container(
                          alignment: Alignment.centerRight,
                          child: Text(
                            series.title,
                            style: TextStyle(
                              fontFamily: 'Cairo',
                              fontSize: titleFontSize,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        );
                      },
                    ),
                  ),

                  const SizedBox(height: 8),

                  // وصف السلسلة
                  Expanded(
                    flex: 2,
                    child: LayoutBuilder(
                      builder: (context, constraints) {
                        // حساب حجم الخط بناءً على عرض البطاقة وحالة الشاشة
                        final isLandscape =
                            MediaQuery.of(context).orientation ==
                                Orientation.landscape;
                        final descFontSize =
                            constraints.maxWidth * (isLandscape ? 0.08 : 0.1);
                        return Container(
                          alignment: Alignment.centerRight,
                          child: Text(
                            series.description,
                            style: TextStyle(
                              fontFamily: 'Cairo',
                              fontSize: descFontSize,
                              color: Colors.white70,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        );
                      },
                    ),
                  ),

                  const SizedBox(height: 8),

                  // نتيجة آخر اختبار
                  Expanded(
                    flex: 3,
                    child: LayoutBuilder(
                      builder: (context, constraints) {
                        // حساب حجم الخط بناءً على عرض البطاقة وحالة الشاشة
                        final isLandscape =
                            MediaQuery.of(context).orientation ==
                                Orientation.landscape;
                        final scoreFontSize =
                            constraints.maxWidth * (isLandscape ? 0.14 : 0.18);
                        return Container(
                          alignment: Alignment.centerRight,
                          child: Text(
                            '${series.lastScore}/${series.totalQuestions}',
                            style: TextStyle(
                              fontFamily: 'Cairo',
                              fontSize: scoreFontSize,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),

            // زر المراجعة في الزاوية العلوية اليسرى
            Positioned(
              top: 8,
              left: 8,
              child: GestureDetector(
                onTap: () {
                  _navigateToReviewScreen(series, context);
                },
                child: Container(
                  width: 30,
                  height: 30,
                  decoration: const BoxDecoration(
                    color: Colors.orange,
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.assessment,
                    color: Colors.white,
                    size: 20,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // دالة للانتقال إلى صفحة المراجعة
  void _navigateToReviewScreen(Series series, BuildContext context) async {
    try {
      // طباعة معلومات تشخيصية
      debugPrint('الانتقال إلى صفحة المراجعة للسلسلة: ${series.id}');

      // إنشاء مزود الاختبار
      final quizProvider = LessonsQuizProvider();

      // تحميل السلسلة مسبقًا
      await quizProvider.loadSeriesById(series.id);

      // الانتقال إلى صفحة المراجعة
      if (!context.mounted) return;

      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => ChangeNotifierProvider.value(
            value: quizProvider,
            child: LessonsReviewScreen(seriesId: series.id),
          ),
        ),
      ).then((_) {
        // تحديث نتيجة آخر اختبار عند العودة من صفحة المراجعة
        _loadLessonsSeries();
      });
    } catch (e) {
      if (!context.mounted) return;

      // عرض رسالة خطأ
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'حدث خطأ أثناء تحميل صفحة المراجعة: $e',
            style: const TextStyle(fontFamily: 'Cairo'),
          ),
          duration: const Duration(seconds: 3),
        ),
      );

      // طباعة تفاصيل الخطأ
      debugPrint('تفاصيل الخطأ: $e');
    }
  }
}
