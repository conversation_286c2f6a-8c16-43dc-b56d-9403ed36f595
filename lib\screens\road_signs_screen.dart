import 'package:flutter/material.dart';
import 'prohibition_signs_screen.dart';
import 'mandatory_signs_screen.dart';
import 'warning_signs_screen.dart';
import 'guidance_signs_screen.dart';
import 'temporary_signs_screen.dart';
import 'minor_signs_screen.dart';
import 'vertical_signs_screen.dart';

class RoadSignsScreen extends StatefulWidget {
  const RoadSignsScreen({Key? key}) : super(key: key);

  @override
  State<RoadSignsScreen> createState() => _RoadSignsScreenState();
}

class _RoadSignsScreenState extends State<RoadSignsScreen> {
  // قائمة بمعلومات الخانات
  final List<Map<String, dynamic>> _categories = [
    {
      'title': 'علامات المنع',
      'image': 'assets/images/road_signs/prohibition_signs.png',
      'onTap': (BuildContext context) {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const ProhibitionSignsScreen(),
          ),
        );
      },
    },
    {
      'title': 'علامات الإجبار',
      'image': 'assets/images/road_signs/mandatory_signs.png',
      'onTap': (BuildContext context) {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const MandatorySignsScreen(),
          ),
        );
      },
    },
    {
      'title': 'علامات الخطر',
      'image': 'assets/images/road_signs/warning_signs.png',
      'onTap': (BuildContext context) {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const WarningSignsScreen(),
          ),
        );
      },
    },
    {
      'title': 'علامات الإرشاد',
      'image': 'assets/images/road_signs/guidance_signs.png',
      'onTap': (BuildContext context) {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const GuidanceSignsScreen(),
          ),
        );
      },
    },
    {
      'title': 'علامات مؤقتة',
      'image': 'assets/images/road_signs/temporary_signs.png',
      'onTap': (BuildContext context) {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const TemporarySignsScreen(),
          ),
        );
      },
    },
    {
      'title': 'علامات صغرى',
      'image': 'assets/images/road_signs/minor_signs.png',
      'onTap': (BuildContext context) {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const MinorSignsScreen(),
          ),
        );
      },
    },
    {
      'title': 'التشوير العمودي',
      'image': 'assets/images/road_signs/vertical_signs.png',
      'onTap': (BuildContext context) {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const VerticalSignsScreen(),
          ),
        );
      },
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true, // تمديد الخلفية خلف شريط التطبيق
      appBar: AppBar(
        title: const Text(
          'التشوير الطرقي',
          style: TextStyle(
            fontFamily: 'Cairo',
            fontWeight: FontWeight.bold,
            color: Colors.white,
            shadows: [
              Shadow(
                offset: Offset(1.0, 1.0),
                blurRadius: 3.0,
                color: Color.fromARGB(150, 0, 0, 0),
              ),
            ],
          ),
        ),
        backgroundColor: Colors.transparent, // جعل الشريط شفافًا
        elevation: 0, // إزالة الظل
        centerTitle: true,
        // إضافة تأثير زجاجي للشريط
        flexibleSpace: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Color.fromRGBO(0, 0, 0, 0.4),
                Color.fromRGBO(0, 0, 0, 0.1),
              ],
            ),
          ),
        ),
        // إزالة leading وإضافة زر العودة في actions على اليمين
        automaticallyImplyLeading: false, // إلغاء زر العودة التلقائي
        actions: [
          IconButton(
            icon: const Icon(
              Icons.arrow_forward,
              color: Colors.white,
              shadows: [
                Shadow(
                  offset: Offset(1.0, 1.0),
                  blurRadius: 3.0,
                  color: Color.fromARGB(150, 0, 0, 0),
                ),
              ],
            ), // سهم للأمام (يمين) للعودة
            onPressed: () {
              Navigator.pop(context);
            },
          ),
        ],
      ),
      body: Container(
        decoration: BoxDecoration(
          // استخدام صورة خلفية مختلفة حسب اتجاه الشاشة
          image: DecorationImage(
            image: AssetImage(
              MediaQuery.of(context).orientation == Orientation.portrait
                  ? 'assets/images/backgrounds/portrait_bg.jpg'
                  : 'assets/images/backgrounds/landscape_bg.jpg',
            ),
            fit: BoxFit.cover,
          ),
        ),
        child: Container(
          // طبقة شفافة فوق الخلفية لتحسين وضوح النص والأزرار
          decoration: const BoxDecoration(
            color: Color.fromRGBO(0, 0, 0, 0.4), // طبقة سوداء شفافة
          ),
          child: Padding(
            // إضافة مسافة إضافية في الأعلى لتعويض الشريط الشفاف
            padding: const EdgeInsets.only(top: 80.0),
            child: OrientationBuilder(
              builder: (context, orientation) {
                // التحقق من اتجاه الشاشة لتحديد عدد الأعمدة
                final isLandscape = orientation == Orientation.landscape;

                return GridView.builder(
                  padding: const EdgeInsets.all(16),
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: isLandscape
                        ? 3
                        : 2, // 3 أعمدة في الوضع الأفقي، 2 في الوضع الرأسي
                    childAspectRatio: 0.85,
                    crossAxisSpacing: 16,
                    mainAxisSpacing: 16,
                  ),
                  itemCount: _categories.length,
                  itemBuilder: (context, index) {
                    return _buildCategoryCard(
                      title: _categories[index]['title'],
                      imagePath: _categories[index]['image'],
                      onTap: () {
                        // التحقق من نوع الفئة واستخدام الدالة المناسبة
                        if (_categories[index]['title'] == 'علامات المنع' ||
                            _categories[index]['title'] == 'علامات الإجبار' ||
                            _categories[index]['title'] == 'علامات الخطر' ||
                            _categories[index]['title'] == 'علامات الإرشاد' ||
                            _categories[index]['title'] == 'علامات مؤقتة' ||
                            _categories[index]['title'] == 'علامات صغرى' ||
                            _categories[index]['title'] == 'التشوير العمودي') {
                          _categories[index]['onTap'](context);
                        } else {
                          // لباقي الفئات، استخدم رسالة "قريبًا"
                          _showComingSoonDialog(context);
                        }
                      },
                    );
                  },
                );
              },
            ),
          ),
        ),
      ),
    );
  }

  // دالة لبناء بطاقة فئة
  Widget _buildCategoryCard({
    required String title,
    required String imagePath,
    required VoidCallback onTap,
  }) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Column(
          children: [
            // صورة الفئة
            Expanded(
              child: ClipRRect(
                borderRadius: const BorderRadius.vertical(
                  top: Radius.circular(12),
                ),
                child: Image.asset(
                  imagePath,
                  fit: BoxFit.cover,
                  width: double.infinity,
                  errorBuilder: (context, error, stackTrace) {
                    // في حالة حدوث خطأ في تحميل الصورة
                    return Container(
                      color: Colors.grey.shade200,
                      child: const Icon(
                        Icons.image_not_supported,
                        size: 50,
                        color: Colors.grey,
                      ),
                    );
                  },
                ),
              ),
            ),
            // عنوان الفئة
            Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(vertical: 12),
              decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.vertical(
                  bottom: Radius.circular(12),
                ),
              ),
              child: Text(
                title,
                style: const TextStyle(
                  fontFamily: 'Cairo',
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // دالة لعرض رسالة "قريبًا"
  void _showComingSoonDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text(
          'قريبًا',
          style: TextStyle(fontFamily: 'Cairo', fontWeight: FontWeight.bold),
          textAlign: TextAlign.center,
        ),
        content: const Text(
          'محتوى هذا القسم قيد الإعداد وسيكون متاحًا قريبًا.',
          style: TextStyle(fontFamily: 'Cairo'),
          textAlign: TextAlign.center,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text(
              'حسناً',
              style:
                  TextStyle(fontFamily: 'Cairo', fontWeight: FontWeight.bold),
            ),
          ),
        ],
      ),
    );
  }
}
