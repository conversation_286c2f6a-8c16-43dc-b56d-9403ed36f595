$jsonFile = "assets/series/series_2/questions.json"
$jsonContent = Get-Content $jsonFile -Raw | ConvertFrom-Json

for ($i = 0; $i -lt $jsonContent.questions.Count; $i++) {
    $questionId = $jsonContent.questions[$i].id
    $jsonContent.questions[$i].audioUrl = "assets/series/series_2/audio/series_2_audio_$questionId.mp3"
}

$jsonContent | ConvertTo-Json -Depth 10 | Set-Content $jsonFile
Write-Host "Updated audio paths in $jsonFile"
