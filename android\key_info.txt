معلومات مفتاح التوقيع لتطبيق سياقة (Siya9a)
=================================

تاريخ الإنشاء: 30 أبريل 2025
تاريخ انتهاء الصلاحية: بعد 10000 يوم (حوالي 27 سنة)

معلومات المفتاح:
---------------
اسم الحزمة: com.mokhtar.siya9a
اسم المفتاح (keyAlias): siya9a_key
كلمة مرور المخزن (storePassword): M@khtaR061340
كلمة مرور المفتاح (keyPassword): M@khtaR061340

معلومات الشهادة:
--------------
الاسم: el mokhtar lakouidssi
الوحدة التنظيمية: mobile
المنظمة: auto ecole ali
المدينة: tantan
المقاطعة: Guelmim-Oued Noun
رمز البلد: MA

ملفات المفتاح:
------------
- siya9a_key.jks: ملف مفتاح التوقيع
- key.properties: ملف إعدادات المفتاح

تعليمات الاستخدام:
---------------
1. ضع ملف siya9a_key.jks في مجلد android/
2. ضع ملف key.properties في مجلد android/
3. قم ببناء التطبيق باستخدام الأمر: flutter build appbundle

ملاحظات مهمة:
-----------
- احتفظ بهذه المعلومات في مكان آمن
- لا تشارك هذه المعلومات مع أي شخص غير موثوق به
- لا تضع هذه الملفات في نظام التحكم بالإصدار مثل Git
- إذا فقدت هذا المفتاح، لن تتمكن من تحديث تطبيقك على Google Play
