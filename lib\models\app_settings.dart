class AppSettings {
  final int timerDuration; // in seconds

  AppSettings({
    required this.timerDuration,
  });

  factory AppSettings.fromJson(Map<String, dynamic> json) {
    return AppSettings(
      timerDuration: json['timerDuration'] ?? 30,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'timerDuration': timerDuration,
    };
  }

  AppSettings copyWith({
    int? timerDuration,
  }) {
    return AppSettings(
      timerDuration: timerDuration ?? this.timerDuration,
    );
  }
}
