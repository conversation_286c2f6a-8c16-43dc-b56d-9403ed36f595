import 'dart:io';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';
import 'package:http/http.dart' as http;

class DownloadService {
  static final DownloadService _instance = DownloadService._internal();
  factory DownloadService() => _instance;
  DownloadService._internal();

  // تحميل صور وملفات صوتية وإجابات صحيحة لسلسلة معينة
  Future<bool> downloadSeries(int seriesId) async {
    try {
      debugPrint('بدء تحميل السلسلة $seriesId');

      // إنشاء المجلدات اللازمة
      final appDir = await getApplicationDocumentsDirectory();
      final seriesDir = Directory('${appDir.path}/series_$seriesId');
      final imagesDir = Directory('${appDir.path}/series_$seriesId/images');
      final audioDir = Directory('${appDir.path}/series_$seriesId/audio');

      if (!await seriesDir.exists()) {
        await seriesDir.create(recursive: true);
        debugPrint('تم إنشاء مجلد السلسلة: ${seriesDir.path}');
      }

      if (!await imagesDir.exists()) {
        await imagesDir.create(recursive: true);
        debugPrint('تم إنشاء مجلد الصور: ${imagesDir.path}');
      }

      if (!await audioDir.exists()) {
        await audioDir.create(recursive: true);
        debugPrint('تم إنشاء مجلد الصوتيات: ${audioDir.path}');
      }

      // تحميل الصور
      final imageUrls = await getSeriesImageUrls(seriesId);
      for (String url in imageUrls) {
        await _downloadAsset(url, seriesId, 'images');
      }

      // تحميل الملفات الصوتية
      final audioUrls = await getSeriesAudioUrls(seriesId);
      for (String url in audioUrls) {
        await _downloadAsset(url, seriesId, 'audio');
      }

      // حفظ الإجابات الصحيحة
      final correctAnswers = await getSeriesCorrectAnswers(seriesId);
      await _saveCorrectAnswers(correctAnswers, seriesId);

      debugPrint('تم تحميل السلسلة $seriesId بنجاح');
      return true;
    } catch (e) {
      debugPrint('خطأ في تحميل السلسلة $seriesId: $e');
      return false;
    }
  }

  // تحميل ملف من الأصول أو من الإنترنت
  Future<void> _downloadAsset(String url, int seriesId, String type) async {
    try {
      final appDir = await getApplicationDocumentsDirectory();
      final fileName = url.split('/').last;
      final filePath = '${appDir.path}/series_$seriesId/$type/$fileName';
      final file = File(filePath);

      debugPrint('جاري تحميل الملف: $url');

      if (url.startsWith('assets/')) {
        // تحميل من الأصول
        try {
          // استخدام rootBundle للوصول إلى ملفات الأصول
          final data = await rootBundle.load(url);
          await file.writeAsBytes(data.buffer.asUint8List());
          debugPrint('تم نسخ الملف من الأصول إلى: $filePath');
        } catch (e) {
          debugPrint('خطأ في نسخ الملف من الأصول: $e');

          // إنشاء ملف بديل في حالة الخطأ
          if (type == 'images') {
            // إنشاء صورة بديلة
            await _createPlaceholderImage(file);
          } else {
            // إنشاء ملف صوتي بديل
            await file.writeAsString('ملف صوتي بديل');
          }
        }
      } else {
        // تحميل من الإنترنت
        try {
          final response = await http.get(Uri.parse(url));
          await file.writeAsBytes(response.bodyBytes);
          debugPrint('تم تحميل الملف من الإنترنت إلى: $filePath');
        } catch (e) {
          debugPrint('خطأ في تحميل الملف من الإنترنت: $e');

          // إنشاء ملف بديل في حالة الخطأ
          if (type == 'images') {
            // إنشاء صورة بديلة
            await _createPlaceholderImage(file);
          } else {
            // إنشاء ملف صوتي بديل
            await file.writeAsString('ملف صوتي بديل');
          }
        }
      }
    } catch (e) {
      debugPrint('خطأ عام في تحميل الملف: $e');
      rethrow;
    }
  }

  // إنشاء صورة بديلة
  Future<void> _createPlaceholderImage(File file) async {
    // إنشاء ملف نصي بسيط كبديل للصورة
    await file.writeAsString('صورة بديلة');
  }

  // حفظ الإجابات الصحيحة
  Future<void> _saveCorrectAnswers(
      Map<int, List<int>> correctAnswers, int seriesId) async {
    try {
      final appDir = await getApplicationDocumentsDirectory();
      final filePath = '${appDir.path}/series_$seriesId/correct_answers.json';

      // تحويل الإجابات الصحيحة إلى JSON
      final Map<String, List<int>> jsonData = {};
      correctAnswers.forEach((key, value) {
        jsonData[key.toString()] = value;
      });

      // حفظ الإجابات الصحيحة
      final file = File(filePath);
      await file.writeAsString(jsonEncode(jsonData));
      debugPrint('تم حفظ الإجابات الصحيحة في: $filePath');
    } catch (e) {
      debugPrint('خطأ في حفظ الإجابات الصحيحة: $e');
      rethrow;
    }
  }

  // الحصول على قائمة الصور للسلسلة
  Future<List<String>> getSeriesImageUrls(int seriesId) async {
    try {
      // قراءة ملف أسئلة السلسلة
      final questionsString = await rootBundle
          .loadString('assets/series/series_$seriesId/questions.json');
      final questionsData = jsonDecode(questionsString);
      final questionsList = questionsData['questions'] as List<dynamic>;

      // استخراج روابط الصور
      List<String> imageUrls = [];
      for (var questionData in questionsList) {
        final imageUrl = questionData['imageUrl'] as String;
        if (!imageUrls.contains(imageUrl)) {
          imageUrls.add(imageUrl);
        }
      }

      return imageUrls;
    } catch (e) {
      debugPrint('خطأ في الحصول على روابط الصور للسلسلة $seriesId: $e');
      // إرجاع روابط افتراضية في حالة الخطأ
      return [
        'https://i.imgur.com/z8xHyT8.jpg',
        'https://i.imgur.com/XqwRRVt.jpg',
      ];
    }
  }

  // الحصول على قائمة الملفات الصوتية للسلسلة
  Future<List<String>> getSeriesAudioUrls(int seriesId) async {
    try {
      // قراءة ملف أسئلة السلسلة
      final questionsString = await rootBundle
          .loadString('assets/series/series_$seriesId/questions.json');
      final questionsData = jsonDecode(questionsString);
      final questionsList = questionsData['questions'] as List<dynamic>;

      // استخراج روابط الملفات الصوتية
      List<String> audioUrls = [];
      for (var questionData in questionsList) {
        final audioUrl = questionData['audioUrl'] as String;
        if (!audioUrls.contains(audioUrl)) {
          audioUrls.add(audioUrl);
        }
      }

      return audioUrls;
    } catch (e) {
      debugPrint(
          'خطأ في الحصول على روابط الملفات الصوتية للسلسلة $seriesId: $e');
      // إرجاع روابط افتراضية في حالة الخطأ
      return [
        'assets/series/series_$seriesId/audio/audio_1.mp3',
      ];
    }
  }

  // الحصول على قائمة الإجابات الصحيحة للسلسلة
  Future<Map<int, List<int>>> getSeriesCorrectAnswers(int seriesId) async {
    try {
      // قراءة ملف أسئلة السلسلة
      final questionsString = await rootBundle
          .loadString('assets/series/series_$seriesId/questions.json');
      final questionsData = jsonDecode(questionsString);
      final questionsList = questionsData['questions'] as List<dynamic>;

      // استخراج الإجابات الصحيحة
      Map<int, List<int>> correctAnswers = {};
      for (var questionData in questionsList) {
        final id = questionData['id'] as int;
        final correctAnswerIndices =
            List<int>.from(questionData['correctAnswerIndices']);
        correctAnswers[id] = correctAnswerIndices;
      }

      return correctAnswers;
    } catch (e) {
      debugPrint('خطأ في الحصول على الإجابات الصحيحة للسلسلة $seriesId: $e');
      // إرجاع إجابات افتراضية في حالة الخطأ
      return {
        1: [0], // الإجابة الصحيحة للسؤال الأول هي الخيار الأول
      };
    }
  }

  // التحقق مما إذا كانت السلسلة محملة
  Future<bool> isSeriesDownloaded(int seriesId) async {
    try {
      final appDir = await getApplicationDocumentsDirectory();
      final seriesDir = Directory('${appDir.path}/series_$seriesId');

      // التحقق من وجود مجلد السلسلة
      if (!await seriesDir.exists()) {
        return false;
      }

      // التحقق من وجود ملف الإجابات الصحيحة
      final correctAnswersFile =
          File('${appDir.path}/series_$seriesId/correct_answers.json');
      if (!await correctAnswersFile.exists()) {
        return false;
      }

      // التحقق من وجود الصور
      final imageUrls = await getSeriesImageUrls(seriesId);
      for (String url in imageUrls) {
        final fileName = url.split('/').last;
        final imageFile =
            File('${appDir.path}/series_$seriesId/images/$fileName');
        if (!await imageFile.exists()) {
          return false;
        }
      }

      // التحقق من وجود الملفات الصوتية
      final audioUrls = await getSeriesAudioUrls(seriesId);
      for (String url in audioUrls) {
        final fileName = url.split('/').last;
        final audioFile =
            File('${appDir.path}/series_$seriesId/audio/$fileName');
        if (!await audioFile.exists()) {
          return false;
        }
      }

      return true;
    } catch (e) {
      debugPrint('خطأ في التحقق من تحميل السلسلة: $e');
      return false;
    }
  }

  // حذف السلسلة المحملة
  Future<bool> deleteSeries(int seriesId) async {
    try {
      final appDir = await getApplicationDocumentsDirectory();
      final seriesDir = Directory('${appDir.path}/series_$seriesId');

      if (await seriesDir.exists()) {
        await seriesDir.delete(recursive: true);
        debugPrint('تم حذف السلسلة: ${seriesDir.path}');
        return true;
      }

      return false;
    } catch (e) {
      debugPrint('خطأ في حذف السلسلة: $e');
      return false;
    }
  }

  // الحصول على مسار الصورة المحلي
  Future<String?> getLocalImagePath(String url, int seriesId) async {
    try {
      final appDir = await getApplicationDocumentsDirectory();
      final fileName = url.split('/').last;
      final filePath = '${appDir.path}/series_$seriesId/images/$fileName';

      final file = File(filePath);
      if (await file.exists()) {
        return filePath;
      }

      return null;
    } catch (e) {
      debugPrint('خطأ في الحصول على مسار الصورة المحلي: $e');
      return null;
    }
  }

  // الحصول على مسار الملف الصوتي المحلي
  Future<String?> getLocalAudioPath(String url, int seriesId) async {
    try {
      final appDir = await getApplicationDocumentsDirectory();
      final fileName = url.split('/').last;
      final filePath = '${appDir.path}/series_$seriesId/audio/$fileName';

      final file = File(filePath);
      if (await file.exists()) {
        return filePath;
      }

      return null;
    } catch (e) {
      debugPrint('خطأ في الحصول على مسار الملف الصوتي المحلي: $e');
      return null;
    }
  }

  // الحصول على الإجابات الصحيحة المحلية
  Future<Map<int, List<int>>?> getLocalCorrectAnswers(int seriesId) async {
    try {
      final appDir = await getApplicationDocumentsDirectory();
      final filePath = '${appDir.path}/series_$seriesId/correct_answers.json';

      final file = File(filePath);
      if (await file.exists()) {
        final jsonString = await file.readAsString();
        final Map<String, dynamic> jsonData = jsonDecode(jsonString);

        // تحويل JSON إلى Map<int, List<int>>
        final Map<int, List<int>> correctAnswers = {};
        jsonData.forEach((key, value) {
          final questionId = int.parse(key);
          final List<dynamic> answers = value;
          correctAnswers[questionId] = answers.map((e) => e as int).toList();
        });

        return correctAnswers;
      }

      return null;
    } catch (e) {
      debugPrint('خطأ في الحصول على الإجابات الصحيحة المحلية: $e');
      return null;
    }
  }
}
