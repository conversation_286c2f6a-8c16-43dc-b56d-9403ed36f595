import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:url_launcher/url_launcher.dart';

class RatingService {
  // مثيل وحيد من الخدمة (Singleton)
  static final RatingService _instance = RatingService._internal();
  factory RatingService() => _instance;
  RatingService._internal();

  // مفاتيح التخزين المحلي
  static const String _hasCompletedSeries1Key = 'has_completed_series_1';
  static const String _hasRatedAppKey = 'has_rated_app';
  static const String _shouldShowRatingDialogKey = 'should_show_rating_dialog';
  static const String _hasShownRatingDialogKey = 'has_shown_rating_dialog';

  // رابط التطبيق على متجر Play Store (استبدله برابط تطبيقك الفعلي)
  static const String _playStoreUrl =
      'https://play.google.com/store/apps/details?id=com.yourcompany.siya9a';

  // دالة لتسجيل اكتمال السلسلة الأولى
  Future<void> markSeries1AsCompleted() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_hasCompletedSeries1Key, true);

    // تعيين علامة لعرض رسالة التقييم
    final hasRatedApp = prefs.getBool(_hasRatedAppKey) ?? false;
    if (!hasRatedApp) {
      await prefs.setBool(_shouldShowRatingDialogKey, true);
      debugPrint('تم تعيين علامة لعرض رسالة التقييم');
    }

    // طباعة حالة التقييم للتحقق
    final hasCompleted = prefs.getBool(_hasCompletedSeries1Key) ?? false;
    final shouldShow = prefs.getBool(_shouldShowRatingDialogKey) ?? false;
    debugPrint('حالة اكتمال السلسلة الأولى: $hasCompleted');
    debugPrint('حالة عرض رسالة التقييم: $shouldShow');
  }

  // دالة للتحقق مما إذا كان يجب عرض رسالة التقييم
  Future<bool> shouldShowRatingDialog() async {
    final prefs = await SharedPreferences.getInstance();
    final hasCompletedSeries1 = prefs.getBool(_hasCompletedSeries1Key) ?? false;
    final hasRatedApp = prefs.getBool(_hasRatedAppKey) ?? false;
    final shouldShowDialog = prefs.getBool(_shouldShowRatingDialogKey) ?? false;
    final hasShownDialog = prefs.getBool(_hasShownRatingDialogKey) ?? false;

    final shouldShow = hasCompletedSeries1 &&
        !hasRatedApp &&
        shouldShowDialog &&
        !hasShownDialog;
    debugPrint('التحقق من عرض رسالة التقييم:');
    debugPrint('- اكتمال السلسلة الأولى: $hasCompletedSeries1');
    debugPrint('- تم تقييم التطبيق: $hasRatedApp');
    debugPrint('- يجب عرض الرسالة: $shouldShowDialog');
    debugPrint('- تم عرض الرسالة سابقاً: $hasShownDialog');
    debugPrint('- النتيجة النهائية: $shouldShow');

    return shouldShow;
  }

  // دالة لتسجيل أن المستخدم قد قيم التطبيق
  Future<void> markAppAsRated() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_hasRatedAppKey, true);
    await prefs.setBool(_shouldShowRatingDialogKey, false);
  }

  // دالة لتسجيل أن المستخدم قد أجل التقييم
  Future<void> postponeRating() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_shouldShowRatingDialogKey, false);
  }

  // دالة لإعادة تعيين حالة التقييم (للاختبار فقط)
  Future<void> resetRatingState() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_hasCompletedSeries1Key);
    await prefs.remove(_hasRatedAppKey);
    await prefs.remove(_shouldShowRatingDialogKey);
    await prefs.remove(_hasShownRatingDialogKey);
    debugPrint('تم إعادة تعيين حالة التقييم');
  }

  // دالة لفتح صفحة التطبيق على متجر Play Store
  Future<void> openPlayStore() async {
    final Uri url = Uri.parse(_playStoreUrl);
    if (await canLaunchUrl(url)) {
      await launchUrl(url, mode: LaunchMode.externalApplication);
      await markAppAsRated();
    } else {
      debugPrint('لا يمكن فتح الرابط: $_playStoreUrl');
    }
  }

  // دالة لتسجيل أن رسالة التقييم قد تم عرضها
  Future<void> markRatingDialogAsShown() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_hasShownRatingDialogKey, true);
    debugPrint('تم تسجيل أن رسالة التقييم قد تم عرضها');
  }

  // دالة لعرض رسالة التقييم
  Future<void> showRatingDialog(BuildContext context) async {
    debugPrint('=== بداية دالة showRatingDialog ===');
    final shouldShow = await shouldShowRatingDialog();
    debugPrint('هل يجب عرض الرسالة؟ $shouldShow');

    if (!shouldShow) {
      debugPrint('لن يتم عرض الرسالة لأن shouldShow = false');
      return;
    }

    if (!context.mounted) {
      debugPrint('لن يتم عرض الرسالة لأن context غير مثبت');
      return;
    }

    // تسجيل أن الرسالة قد تم عرضها
    debugPrint('تسجيل أن الرسالة قد تم عرضها');
    await markRatingDialogAsShown();

    if (!context.mounted) {
      debugPrint(
          'لن يتم عرض الرسالة بعد markRatingDialogAsShown لأن context غير مثبت');
      return;
    }

    debugPrint('عرض رسالة التقييم');
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.star,
              color: Colors.amber,
              size: 30,
            ),
            SizedBox(width: 10),
            Text(
              'تقييم التطبيق',
              style: TextStyle(fontFamily: 'Cairo'),
            ),
          ],
        ),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.rate_review,
              color: Colors.amber,
              size: 60,
            ),
            SizedBox(height: 16),
            Text(
              'هل أعجبك التطبيق؟ يرجى تقييمه على متجر Play Store لمساعدتنا على التحسين.',
              style: TextStyle(fontFamily: 'Cairo'),
              textAlign: TextAlign.center,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              postponeRating();
            },
            child: const Text('لاحقاً', style: TextStyle(fontFamily: 'Cairo')),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              openPlayStore();
            },
            child: const Text('تقييم الآن',
                style: TextStyle(fontFamily: 'Cairo', color: Colors.amber)),
          ),
        ],
      ),
    );
  }

  // دالة لعرض رسالة التقييم بشكل إجباري بدون تحقق من الشروط
  Future<void> showRatingDialogForced(BuildContext context) async {
    debugPrint('=== بداية دالة showRatingDialogForced ===');

    // إعادة تعيين حالة التقييم أولاً
    await resetRatingState();
    debugPrint('تم إعادة تعيين حالة التقييم');

    // تسجيل اكتمال السلسلة الأولى
    await markSeries1AsCompleted();
    debugPrint('تم تسجيل اكتمال السلسلة الأولى');

    if (!context.mounted) {
      debugPrint('لن يتم عرض الرسالة لأن context غير مثبت');
      return;
    }

    debugPrint('عرض رسالة التقييم بشكل إجباري');
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.star,
              color: Colors.amber,
              size: 30,
            ),
            SizedBox(width: 10),
            Text(
              'تقييم التطبيق',
              style: TextStyle(fontFamily: 'Cairo'),
            ),
          ],
        ),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.rate_review,
              color: Colors.amber,
              size: 60,
            ),
            SizedBox(height: 16),
            Text(
              'هل أعجبك التطبيق؟ يرجى تقييمه على متجر Play Store لمساعدتنا على التحسين.',
              style: TextStyle(fontFamily: 'Cairo'),
              textAlign: TextAlign.center,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              postponeRating();
            },
            child: const Text('لاحقاً', style: TextStyle(fontFamily: 'Cairo')),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              openPlayStore();
            },
            child: const Text('تقييم الآن',
                style: TextStyle(fontFamily: 'Cairo', color: Colors.amber)),
          ),
        ],
      ),
    );
  }
}
