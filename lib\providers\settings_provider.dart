import 'package:flutter/foundation.dart';
import '../models/app_settings.dart';
import '../services/settings_service.dart';

class SettingsProvider with ChangeNotifier {
  AppSettings _settings = AppSettings(timerDuration: 30);
  final SettingsService _settingsService = SettingsService();
  bool _isLoading = true;

  SettingsProvider() {
    _loadSettings();
  }

  AppSettings get settings => _settings;
  bool get isLoading => _isLoading;

  Future<void> _loadSettings() async {
    _settings = await _settingsService.getSettings();
    _isLoading = false;
    notifyListeners();
  }

  Future<void> updateTimerDuration(int seconds) async {
    _settings = _settings.copyWith(timerDuration: seconds);
    await _settingsService.saveSettings(_settings);
    notifyListeners();
  }
}
