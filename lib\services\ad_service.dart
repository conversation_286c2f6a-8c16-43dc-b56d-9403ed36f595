import 'package:flutter/foundation.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';

class AdService {
  // معرفات الإعلانات الحقيقية
  static const String _interstitialAdUnitId =
      'ca-app-pub-5259090144954556/3459081435'; // معرف الإعلان البيني الحقيقي

  InterstitialAd? _interstitialAd;
  bool _isInterstitialAdReady = false;
  bool _isLoadingAd = false;

  // الحصول على حالة جاهزية الإعلان
  bool get isInterstitialAdReady => _isInterstitialAdReady;

  // تهيئة خدمة الإعلانات
  Future<void> initialize() async {
    // تخطي تهيئة الإعلانات إذا كان التطبيق يعمل على الويب
    if (kIsWeb) {
      debugPrint(
          'تم تخطي تهيئة الإعلانات في AdService لأن التطبيق يعمل على الويب');
      return;
    }

    try {
      await MobileAds.instance.initialize();
      debugPrint('تم تهيئة خدمة الإعلانات بنجاح');
    } catch (e) {
      debugPrint('فشل في تهيئة خدمة الإعلانات: $e');
    }
  }

  // تحميل إعلان بيني
  Future<void> loadInterstitialAd() async {
    // تخطي تحميل الإعلان إذا كان التطبيق يعمل على الويب
    if (kIsWeb) {
      debugPrint(
          'تم تخطي تحميل الإعلان في AdService لأن التطبيق يعمل على الويب');
      return;
    }

    if (_isInterstitialAdReady) {
      debugPrint('الإعلان البيني جاهز بالفعل');
      return;
    }

    if (_isLoadingAd) {
      debugPrint('جاري تحميل الإعلان بالفعل');
      return;
    }

    _isLoadingAd = true;
    debugPrint('جاري تحميل الإعلان البيني...');

    try {
      await InterstitialAd.load(
        adUnitId: _interstitialAdUnitId,
        request: const AdRequest(),
        adLoadCallback: InterstitialAdLoadCallback(
          onAdLoaded: (ad) {
            _interstitialAd = ad;
            _isInterstitialAdReady = true;
            _isLoadingAd = false;

            // إعداد مستمع لإغلاق الإعلان
            _interstitialAd!.fullScreenContentCallback =
                FullScreenContentCallback(
              onAdDismissedFullScreenContent: (ad) {
                _isInterstitialAdReady = false;
                _interstitialAd = null;
                debugPrint('تم إغلاق الإعلان البيني');
              },
              onAdFailedToShowFullScreenContent: (ad, error) {
                _isInterstitialAdReady = false;
                _interstitialAd = null;
                ad.dispose();
                debugPrint('فشل عرض الإعلان البيني: $error');
              },
              onAdShowedFullScreenContent: (ad) {
                debugPrint('تم عرض الإعلان بنجاح');
              },
            );

            debugPrint('تم تحميل الإعلان البيني بنجاح');
          },
          onAdFailedToLoad: (error) {
            _isInterstitialAdReady = false;
            _isLoadingAd = false;
            debugPrint('فشل تحميل الإعلان البيني: ${error.message}');
          },
        ),
      );
    } catch (e) {
      _isInterstitialAdReady = false;
      _isLoadingAd = false;
      debugPrint('حدث خطأ أثناء تحميل الإعلان البيني: $e');
    }
  }

  // عرض الإعلان البيني
  Future<bool> showInterstitialAd() async {
    // تخطي عرض الإعلان إذا كان التطبيق يعمل على الويب
    if (kIsWeb) {
      debugPrint('تم تخطي عرض الإعلان في AdService لأن التطبيق يعمل على الويب');
      return false;
    }

    if (!_isInterstitialAdReady || _interstitialAd == null) {
      debugPrint('الإعلان البيني غير جاهز للعرض');
      return false;
    }

    bool adShown = false;

    try {
      await _interstitialAd!.show();
      adShown = true;
      _isInterstitialAdReady = false;
      debugPrint('تم عرض الإعلان البيني');
    } catch (e) {
      debugPrint('حدث خطأ أثناء عرض الإعلان البيني: $e');
      _isInterstitialAdReady = false;
      _interstitialAd = null;
    }

    return adShown;
  }

  // التخلص من الإعلان عند عدم الحاجة إليه
  void dispose() {
    _interstitialAd?.dispose();
    _interstitialAd = null;
    _isInterstitialAdReady = false;
  }
}
