import 'package:flutter/material.dart';

class WarningSignsScreen extends StatefulWidget {
  const WarningSignsScreen({Key? key}) : super(key: key);

  @override
  State<WarningSignsScreen> createState() => _WarningSignsScreenState();
}

class _WarningSignsScreenState extends State<WarningSignsScreen> {
  // قائمة بمعلومات علامات الخطر
  final List<Map<String, dynamic>> _warningSigns = [
    {
      'image': 'assets/images/road_signs/warning/sign1.png',
      'description': 'منعطف خطير',
    },
    {
      'image': 'assets/images/road_signs/warning/sign2.png',
      'description': 'تقاطع طرق',
    },
    {
      'image': 'assets/images/road_signs/warning/sign3.png',
      'description': 'أشغال في الطريق',
    },
    {
      'image': 'assets/images/road_signs/warning/sign4.png',
      'description': 'ممر للمشاة',
    },
    {
      'image': 'assets/images/road_signs/warning/sign5.png',
      'description': 'خطر الانزلاق',
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'علامات الخطر',
          style: TextStyle(
            fontFamily: 'Cairo',
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: const Color(0xFF2196F3),
        centerTitle: true,
        // إزالة leading وإضافة زر العودة في actions على اليمين
        automaticallyImplyLeading: false, // إلغاء زر العودة التلقائي
        actions: [
          IconButton(
            icon: const Icon(Icons.arrow_forward), // سهم للأمام (يمين) للعودة
            onPressed: () {
              Navigator.pop(context);
            },
          ),
        ],
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.blue.shade400,
              Colors.grey.shade800,
            ],
          ),
        ),
        child: OrientationBuilder(
          builder: (context, orientation) {
            // التحقق من اتجاه الشاشة لتحديد عدد الأعمدة
            final isLandscape = orientation == Orientation.landscape;

            return GridView.builder(
              padding: const EdgeInsets.all(16),
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: isLandscape
                    ? 2
                    : 1, // عمودان في الوضع الأفقي، عمود واحد في الوضع الرأسي
                childAspectRatio:
                    1, // نسبة العرض إلى الارتفاع 1:1 للحفاظ على الشكل المربع
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
              ),
              itemCount: _warningSigns.length,
              itemBuilder: (context, index) {
                return _buildSignCard(
                  imagePath: _warningSigns[index]['image'],
                  description: _warningSigns[index]['description'],
                );
              },
            );
          },
        ),
      ),
    );
  }

  // دالة لبناء بطاقة علامة الخطر
  Widget _buildSignCard({
    required String imagePath,
    required String description,
  }) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // حساب عرض البطاقة بناءً على عرض الشاشة
        // استخدام نسبة من عرض الشاشة مع هوامش
        final screenWidth = MediaQuery.of(context).size.width;
        final cardSize = screenWidth > 600
            ? screenWidth * 0.4 // للشاشات الكبيرة
            : screenWidth * 0.8; // للشاشات الصغيرة

        // التأكد من أن البطاقة مربعة
        final cardWidth = cardSize;
        final cardHeight = cardSize;

        return Center(
          child: Card(
            margin: const EdgeInsets.only(bottom: 16),
            elevation: 4,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            child: AspectRatio(
              aspectRatio:
                  1, // نسبة العرض إلى الارتفاع 1:1 للحفاظ على الشكل المربع
              child: ClipRRect(
                borderRadius: BorderRadius.circular(12),
                child: Image.asset(
                  imagePath,
                  width: cardWidth,
                  height: cardHeight,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    // في حالة حدوث خطأ في تحميل الصورة
                    return Container(
                      width: cardWidth,
                      height: cardHeight,
                      color: Colors.grey.shade200,
                      child: const Center(
                        child: Icon(
                          Icons.image_not_supported,
                          size: 50,
                          color: Colors.grey,
                        ),
                      ),
                    );
                  },
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
