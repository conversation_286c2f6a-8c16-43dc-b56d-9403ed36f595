import 'package:flutter/material.dart';
import 'vehicle_documents_screen.dart';
import 'vehicle_lights_screen.dart';
import 'vehicle_systems_screen.dart';
import 'vehicle_monitoring_screen.dart';

class VehiclesScreen extends StatefulWidget {
  const VehiclesScreen({Key? key}) : super(key: key);

  @override
  State<VehiclesScreen> createState() => _VehiclesScreenState();
}

class _VehiclesScreenState extends State<VehiclesScreen> {
  // قائمة بفئات بطاقة المركبة
  final List<Map<String, dynamic>> _categories = [
    {
      'title': 'وثائق المركبة',
      'image': 'assets/images/vehicles/vehicle_documents.png',
      'onTap': (BuildContext context) {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const VehicleDocumentsScreen(),
          ),
        );
      },
    },
    {
      'title': 'أضواء المركبة',
      'image': 'assets/images/vehicles/vehicle_types.png',
      'onTap': (BuildContext context) {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const VehicleLightsScreen(),
          ),
        );
      },
    },
    {
      'title': 'أنظمة المركبة',
      'image': 'assets/images/vehicles/vehicle_parts.png',
      'onTap': (BuildContext context) {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const VehicleSystemsScreen(),
          ),
        );
      },
    },
    {
      'title': 'مراقبة المركبة',
      'image': 'assets/images/vehicles/vehicle_maintenance.png',
      'onTap': (BuildContext context) {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const VehicleMonitoringScreen(),
          ),
        );
      },
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true, // تمديد الخلفية خلف شريط التطبيق
      appBar: AppBar(
        title: const Text(
          'المركبات',
          style: TextStyle(
            fontFamily: 'Cairo',
            fontWeight: FontWeight.bold,
            color: Colors.white,
            shadows: [
              Shadow(
                offset: Offset(1.0, 1.0),
                blurRadius: 3.0,
                color: Color.fromARGB(150, 0, 0, 0),
              ),
            ],
          ),
        ),
        backgroundColor: Colors.transparent, // جعل الشريط شفافًا
        elevation: 0, // إزالة الظل
        centerTitle: true,
        // إضافة تأثير زجاجي للشريط
        flexibleSpace: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Color.fromRGBO(0, 0, 0, 0.4),
                Color.fromRGBO(0, 0, 0, 0.1),
              ],
            ),
          ),
        ),
        // إزالة leading وإضافة زر العودة في actions على اليمين
        automaticallyImplyLeading: false, // إلغاء زر العودة التلقائي
        actions: [
          IconButton(
            icon: const Icon(
              Icons.arrow_forward,
              color: Colors.white,
              shadows: [
                Shadow(
                  offset: Offset(1.0, 1.0),
                  blurRadius: 3.0,
                  color: Color.fromARGB(150, 0, 0, 0),
                ),
              ],
            ), // سهم للأمام (يمين) للعودة
            onPressed: () {
              Navigator.pop(context);
            },
          ),
        ],
      ),
      body: Container(
        decoration: BoxDecoration(
          // استخدام صورة خلفية مختلفة حسب اتجاه الشاشة
          image: DecorationImage(
            image: AssetImage(
              MediaQuery.of(context).orientation == Orientation.portrait
                  ? 'assets/images/backgrounds/portrait_bg.jpg'
                  : 'assets/images/backgrounds/landscape_bg.jpg',
            ),
            fit: BoxFit.cover,
          ),
        ),
        child: Container(
          // طبقة شفافة فوق الخلفية لتحسين وضوح النص والأزرار
          decoration: const BoxDecoration(
            color: Color.fromRGBO(0, 0, 0, 0.4), // طبقة سوداء شفافة
          ),
          child: Padding(
            // إضافة مسافة إضافية في الأعلى لتعويض الشريط الشفاف
            padding: const EdgeInsets.only(top: 80.0),
            child: OrientationBuilder(
              builder: (context, orientation) {
                // التحقق من اتجاه الشاشة لتحديد عدد الأعمدة
                final isLandscape = orientation == Orientation.landscape;

                return GridView.builder(
                  padding: const EdgeInsets.all(16),
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: isLandscape
                        ? 3
                        : 2, // 3 أعمدة في الوضع الأفقي، 2 في الوضع الرأسي
                    childAspectRatio: 0.85,
                    crossAxisSpacing: 16,
                    mainAxisSpacing: 16,
                  ),
                  itemCount: _categories.length,
                  itemBuilder: (context, index) {
                    return _buildCategoryCard(
                      title: _categories[index]['title'],
                      imagePath: _categories[index]['image'],
                      onTap: () {
                        _categories[index]['onTap'](context);
                      },
                    );
                  },
                );
              },
            ),
          ),
        ),
      ),
    );
  }

  // دالة لبناء بطاقة فئة
  Widget _buildCategoryCard({
    required String title,
    required String imagePath,
    required VoidCallback onTap,
  }) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Column(
          children: [
            // صورة الفئة
            Expanded(
              child: ClipRRect(
                borderRadius: const BorderRadius.vertical(
                  top: Radius.circular(12),
                ),
                child: Image.asset(
                  imagePath,
                  fit: BoxFit.cover,
                  width: double.infinity,
                  errorBuilder: (context, error, stackTrace) {
                    // في حالة حدوث خطأ في تحميل الصورة
                    return Container(
                      color: Colors.grey.shade200,
                      child: const Icon(
                        Icons.image_not_supported,
                        size: 50,
                        color: Colors.grey,
                      ),
                    );
                  },
                ),
              ),
            ),
            // عنوان الفئة
            Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(vertical: 12),
              decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.vertical(
                  bottom: Radius.circular(12),
                ),
              ),
              child: Text(
                title,
                style: const TextStyle(
                  fontFamily: 'Cairo',
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
